/**
* Template Name: BharatX Events
* Template URL: https://bootstrapmade.com/BharatX Events-bootstrap-events-template/
* Updated: Jul 19 2025 with Bootstrap v5.3.7
* Author: BootstrapMade.com
* License: https://bootstrapmade.com/license/
*/

@charset "UTF-8";

/*--------------------------------------------------------------
# Font & Color Variables
# Help: https://bootstrapmade.com/color-system/
--------------------------------------------------------------*/
/* Fonts */
:root {
  --default-font: "Roboto", system-ui, -apple-system, "Segoe UI", <PERSON><PERSON>,
    "Helvetica Neue", <PERSON><PERSON>, "Noto Sans", "Liberation Sans", sans-serif,
    "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  --heading-font: "Rubik", sans-serif;
  --nav-font: "Kanit", sans-serif;
  text-transform: capitalize;
}

/* Global Colors - The following color variables are used throughout the website. Updating them here will change the color scheme of the entire website */
:root {
  --background-color: #ffffff; /* Background color for the entire website, including individual sections */
  --default-color: #40372e; /* Default color used for the majority of the text content across the entire website */
  --heading-color: #281a0c; /* Color for headings, subheadings and title throughout the website */
  --accent-color: #f87500; /* Accent color that represents your brand on the website. It's used for buttons, links, and other elements that need to stand out */
  --surface-color: #ffffff; /* The surface color is used as a background of boxed elements within sections, such as cards, icon boxes, or other elements that require a visual separation from the global background. */
  --contrast-color: #ffffff; /* Contrast color for text, ensuring readability against backgrounds of accent, heading, or default colors. */
}

/* Nav Menu Colors - The following color variables are used specifically for the navigation menu. They are separate from the global colors to allow for more customization options */
:root {
  --nav-color: #ffffff; /* The default color of the main navmenu links */
  --nav-hover-color: #f87500; /* Applied to main navmenu links when they are hovered over or active */
  --nav-mobile-background-color: #ffffff; /* Used as the background color for mobile navigation menu */
  --nav-dropdown-background-color: #ffffff; /* Used as the background color for dropdown items that appear when hovering over primary navigation items */
  --nav-dropdown-color: #40372e; /* Used for navigation links of the dropdown items in the navigation menu. */
  --nav-dropdown-hover-color: #f87500; /* Similar to --nav-hover-color, this color is applied to dropdown navigation links when they are hovered over. */
}

/* Color Presets - These classes override global colors when applied to any section or element, providing reuse of the sam color scheme. */

.light-background {
  --background-color: #fffbf7;
  --surface-color: #ffffff;
}

.dark-background {
  --background-color: #100901;
  --default-color: #ffffff;
  --heading-color: #ffffff;
  --surface-color: #252525;
  --contrast-color: #ffffff;
}

/* Smooth scroll */
:root {
  scroll-behavior: smooth;
}

/*--------------------------------------------------------------
# General Styling & Shared Classes
--------------------------------------------------------------*/
body {
  color: var(--default-color);
  background-color: var(--background-color);
  font-family: var(--default-font);
}

a {
  color: var(--accent-color);
  text-decoration: none;
  transition: 0.3s;
}

a:hover {
  color: color-mix(in srgb, var(--accent-color), transparent 25%);
  text-decoration: none;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  color: var(--heading-color);
  font-family: var(--heading-font);
}

/* PHP Email Form Messages
------------------------------*/
.php-email-form .error-message {
  display: none;
  background: #df1529;
  color: #ffffff;
  text-align: left;
  padding: 15px;
  margin-bottom: 24px;
  font-weight: 600;
}

.php-email-form .sent-message {
  display: none;
  color: #ffffff;
  background: #059652;
  text-align: center;
  padding: 15px;
  margin-bottom: 24px;
  font-weight: 600;
}

.php-email-form .loading {
  display: none;
  background: var(--surface-color);
  text-align: center;
  padding: 15px;
  margin-bottom: 24px;
}

.php-email-form .loading:before {
  content: "";
  display: inline-block;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  margin: 0 10px -6px 0;
  border: 3px solid var(--accent-color);
  border-top-color: var(--surface-color);
  animation: php-email-form-loading 1s linear infinite;
}

@keyframes php-email-form-loading {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

/*--------------------------------------------------------------
# Global Header
--------------------------------------------------------------*/
.header {
  --background-color: rgba(255, 255, 255, 0);
  --default-color: #ffffff;
  --heading-color: #ffffff;
  color: var(--default-color);
  background-color: var(--background-color);
  /* padding: 15px 0; */
  transition: all 0.5s;
  z-index: 997;
}

.header .logo {
  line-height: 1;
}

.header .logo img {
  max-height: 120px;
  margin-right: 8px;
}

.header .logo h1 {
  font-size: 30px;
  margin: 0;
  font-weight: 700;
  color: var(--heading-color);
}

.header .btn-getstarted,
.header .btn-getstarted:focus {
  color: var(--contrast-color);
  background: var(--accent-color);
  font-size: 14px;
  padding: 8px 25px;
  margin: 0 0 0 30px;
  border-radius: 50px;
  transition: 0.3s;
}

.header .btn-getstarted:hover,
.header .btn-getstarted:focus:hover {
  color: var(--contrast-color);
  background: color-mix(in srgb, var(--accent-color), transparent 15%);
}

@media (max-width: 1200px) {
  .header .logo {
    order: 1;
  }

  .header .btn-getstarted {
    order: 2;
    margin: 0 0 0 70px;
    padding: 6px 15px;
    width: 100px;
  }

  .header .navmenu {
    order: 3;
  }
}

.scrolled .header {
  box-shadow: 0px 0 18px rgba(0, 0, 0, 0.1);
}

/* Global Header on Scroll
------------------------------*/
.scrolled .header {
  --background-color: rgb(34, 18, 2);
  z-index: 9999999;
}

/*--------------------------------------------------------------
# Navigation Menu
--------------------------------------------------------------*/
/* Navmenu - Desktop */
@media (min-width: 1200px) {
  .navmenu {
    padding: 0;
  }

  .navmenu ul {
    margin: 0;
    padding: 0;
    display: flex;
    list-style: none;
    align-items: center;
  }

  .navmenu li {
    position: relative;
  }

  .navmenu a,
  .navmenu a:focus {
    color: var(--nav-color);
    padding: 18px 15px;
    font-size: 16px;
    font-family: var(--nav-font);
    font-weight: 400;
    display: flex;
    align-items: center;
    justify-content: space-between;
    white-space: nowrap;
    transition: 0.3s;
  }

  .navmenu a i,
  .navmenu a:focus i {
    font-size: 12px;
    line-height: 0;
    margin-left: 5px;
    transition: 0.3s;
  }

  .navmenu li:last-child a {
    padding-right: 0;
  }

  .navmenu li:hover > a,
  .navmenu .active,
  .navmenu .active:focus {
    color: var(--nav-hover-color);
  }

  .navmenu .dropdown ul {
    margin: 0;
    padding: 10px 0;
    background: var(--nav-dropdown-background-color);
    display: block;
    position: absolute;
    visibility: hidden;
    left: 14px;
    top: 130%;
    opacity: 0;
    transition: 0.3s;
    border-radius: 4px;
    z-index: 99;
    box-shadow: 0px 0px 30px rgba(0, 0, 0, 0.1);
  }

  .navmenu .dropdown ul li {
    min-width: 200px;
  }

  .navmenu .dropdown ul a {
    padding: 10px 20px;
    font-size: 15px;
    text-transform: none;
    color: var(--nav-dropdown-color);
  }

  .navmenu .dropdown ul a i {
    font-size: 12px;
  }

  .navmenu .dropdown ul a:hover,
  .navmenu .dropdown ul .active,
  .navmenu .dropdown ul .active:hover,
  .navmenu .dropdown ul li:hover > a {
    color: var(--nav-dropdown-hover-color);
  }

  .navmenu .dropdown:hover > ul {
    opacity: 1;
    top: 100%;
    visibility: visible;
  }

  .navmenu .dropdown .dropdown ul {
    top: 0;
    left: -90%;
    visibility: hidden;
  }

  .navmenu .dropdown .dropdown:hover > ul {
    opacity: 1;
    top: 0;
    left: -100%;
    visibility: visible;
  }
}

/* Navmenu - Mobile */
@media (max-width: 1199px) {
  .mobile-nav-toggle {
    color: var(--nav-color);
    font-size: 28px;
    line-height: 0;
    margin-right: 10px;
    cursor: pointer;
    transition: color 0.3s;
  }

  .navmenu {
    padding: 0;
    z-index: 9997;
  }

  .navmenu ul {
    display: none;
    list-style: none;
    position: absolute;
    inset: 60px 20px 20px 20px;
    padding: 10px 0;
    margin: 0;
    border-radius: 6px;
    background-color: var(--nav-mobile-background-color);
    overflow-y: auto;
    transition: 0.3s;
    z-index: 9998;
    box-shadow: 0px 0px 30px rgba(0, 0, 0, 0.1);
  }

  .navmenu a,
  .navmenu a:focus {
    color: var(--nav-dropdown-color);
    padding: 10px 20px;
    font-family: var(--nav-font);
    font-size: 17px;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: space-between;
    white-space: nowrap;
    transition: 0.3s;
  }

  .navmenu a i,
  .navmenu a:focus i {
    font-size: 12px;
    line-height: 0;
    margin-left: 5px;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: 0.3s;
    background-color: color-mix(in srgb, var(--accent-color), transparent 90%);
  }

  .navmenu a i:hover,
  .navmenu a:focus i:hover {
    background-color: var(--accent-color);
    color: var(--contrast-color);
  }

  .navmenu a:hover,
  .navmenu .active,
  .navmenu .active:focus {
    color: var(--nav-dropdown-hover-color);
  }

  .navmenu .active i,
  .navmenu .active:focus i {
    background-color: var(--accent-color);
    color: var(--contrast-color);
    transform: rotate(180deg);
  }

  .navmenu .dropdown ul {
    position: static;
    display: none;
    z-index: 99;
    padding: 10px 0;
    margin: 10px 20px;
    background-color: var(--nav-dropdown-background-color);
    border: 1px solid color-mix(in srgb, var(--default-color), transparent 90%);
    box-shadow: none;
    transition: all 0.5s ease-in-out;
  }

  .navmenu .dropdown ul ul {
    background-color: rgba(33, 37, 41, 0.1);
  }

  .navmenu .dropdown > .dropdown-active {
    display: block;
    background-color: rgba(33, 37, 41, 0.03);
  }

  .mobile-nav-active {
    overflow: hidden;
  }

  .mobile-nav-active .mobile-nav-toggle {
    color: #fff;
    position: absolute;
    font-size: 32px;
    top: 15px;
    right: 15px;
    margin-right: 0;
    z-index: 9999;
  }

  .mobile-nav-active .navmenu {
    position: fixed;
    overflow: hidden;
    inset: 0;
    background: rgba(33, 37, 41, 0.8);
    transition: 0.3s;
  }

  .mobile-nav-active .navmenu > ul {
    display: block;
  }
}

/*--------------------------------------------------------------
# Global Footer
--------------------------------------------------------------*/
.footer {
  color: var(--default-color);
  background-color: var(--background-color);
  font-size: 14px;
  position: relative;
  border-top-left-radius: 10px; /* Added */
  border-top-right-radius: 10px; /* Added */
}

.footer .footer-top {
  padding-top: 60px; /* Increased */
  padding-bottom: 30px; /* Added */
  border-top: 1px solid
    color-mix(in srgb, var(--default-color), transparent 90%);
}

.footer .footer-about .logo {
  line-height: 1;
  margin-bottom: 25px;
}

.footer .footer-about .logo img {
  max-height: 40px;
  margin-right: 6px;
}

.footer .footer-about .logo span {
  color: var(--heading-color);
  font-family: var(--heading-font);
  font-size: 26px;
  font-weight: 700;
  letter-spacing: 1px;
}

.footer .footer-about p {
  font-size: 14px;
  font-family: var(--heading-font);
}

.footer .social-links a {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: 1px solid color-mix(in srgb, var(--default-color), transparent 50%);
  font-size: 16px;
  color: color-mix(in srgb, var(--default-color), transparent 20%);
  margin-right: 10px;
  transition: 0.3s;
}

.footer .social-links a:hover {
  color: var(--accent-color);
  border-color: var(--accent-color);
}

.footer h4 {
  font-size: 16px;
  font-weight: bold;
  position: relative;
  padding-bottom: 12px;
}

.footer .footer-links {
  margin-bottom: 30px;
}

.footer .footer-links ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer .footer-links ul i {
  padding-right: 2px;
  font-size: 12px;
  line-height: 0;
}

.footer .footer-links ul li {
  padding: 10px 0;
  display: flex;
  align-items: center;
}

.footer .footer-links ul li:first-child {
  padding-top: 0;
}

.footer .footer-links ul a {
  color: color-mix(in srgb, var(--default-color), transparent 30%);
  display: inline-block;
  line-height: 1;
}

.footer .footer-links ul a:hover {
  color: var(--accent-color);
}

.footer .footer-contact p {
  margin-bottom: 5px;
}

.footer .copyright {
  padding: 30px 0; /* Increased */
  border-top: 1px solid
    color-mix(in srgb, var(--default-color), transparent 90%);
}

.footer .copyright p {
  margin-bottom: 0;
}

.footer .credits {
  margin-top: 8px;
  font-size: 13px;
}

/*--------------------------------------------------------------
# Preloader
--------------------------------------------------------------*/
#preloader {
  position: fixed;
  inset: 0;
  z-index: 999999;
  overflow: hidden;
  background: var(--background-color);
  transition: all 0.6s ease-out;
}

#preloader:before {
  content: "";
  position: fixed;
  top: calc(50% - 30px);
  left: calc(50% - 30px);
  border: 6px solid #ffffff;
  border-color: var(--accent-color) transparent var(--accent-color) transparent;
  border-radius: 50%;
  width: 60px;
  height: 60px;
  animation: animate-preloader 1.5s linear infinite;
}

@keyframes animate-preloader {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

/*--------------------------------------------------------------
# Scroll Top Button
--------------------------------------------------------------*/
.scroll-top {
  position: fixed;
  visibility: hidden;
  opacity: 0;
  right: 15px;
  bottom: 15px;
  z-index: 99999;
  background-color: var(--accent-color);
  width: 40px;
  height: 40px;
  border-radius: 4px;
  transition: all 0.4s;
}

.scroll-top i {
  font-size: 24px;
  color: var(--contrast-color);
  line-height: 0;
}

.scroll-top:hover {
  background-color: color-mix(in srgb, var(--accent-color), transparent 20%);
  color: var(--contrast-color);
}

.scroll-top.active {
  visibility: visible;
  opacity: 1;
}

/*--------------------------------------------------------------
# Global Page Titles & Breadcrumbs
--------------------------------------------------------------*/
.page-title {
  color: var(--default-color);
  background-color: var(--background-color);
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  padding: 160px 0 80px 0;
  text-align: center;
  position: relative;
}

.page-title:before {
  content: "";
  background-color: color-mix(
    in srgb,
    var(--background-color),
    transparent 30%
  );
  position: absolute;
  inset: 0;
}

.page-title h1 {
  font-size: 42px;
  font-weight: 700;
  margin-bottom: 10px;
}

.page-title .breadcrumbs ol {
  display: flex;
  flex-wrap: wrap;
  list-style: none;
  justify-content: center;
  padding: 0;
  margin: 0;
  font-size: 16px;
  font-weight: 400;
}

.page-title .breadcrumbs ol li + li {
  padding-left: 10px;
}

.page-title .breadcrumbs ol li + li::before {
  content: "/";
  display: inline-block;
  padding-right: 10px;
  color: color-mix(in srgb, var(--default-color), transparent 50%);
}

/*--------------------------------------------------------------
# Global Sections
--------------------------------------------------------------*/
section,
.section {
  color: var(--default-color);
  /* background-color: var(--background-color); */
  padding: 30px 0;
  scroll-margin-top: 90px;
  overflow: clip;
}

@media (max-width: 1199px) {
  section,
  .section {
    scroll-margin-top: 66px;
  }
}

/*--------------------------------------------------------------
# Global Section Titles
--------------------------------------------------------------*/
.section-title {
  text-align: center;
  padding-bottom: 60px;
  padding-top: 60px;
  position: relative;
}

.section-title h2 {
  font-size: 32px;
  font-weight: 700;
  margin-bottom: 20px;
  padding-bottom: 20px;
  position: relative;
}

.section-title h2:before {
  content: "";
  position: absolute;
  display: block;
  width: 160px;
  height: 1px;
  background: color-mix(in srgb, var(--default-color), transparent 60%);
  left: 0;
  right: 0;
  bottom: 1px;
  margin: auto;
}

.section-title h2::after {
  content: "";
  position: absolute;
  display: block;
  width: 60px;
  height: 3px;
  background: var(--accent-color);
  left: 0;
  right: 0;
  bottom: 0;
  margin: auto;
}

.section-title p {
  margin-bottom: 0;
}

/*--------------------------------------------------------------
# Service Cards Background Images
--------------------------------------------------------------*/
.service-card-luxury {
  background-image: url("https://www.visionevents.in/wp-content/uploads/2022/05/LUXURY-BIRTHDAY-Vision-Events.jpg") !important;
}

.service-card-photography {
  background-image: url("https://cdn-blog.superprof.com/blog_in/wp-content/uploads/2020/01/in-photo-photo-1.jpg") !important;
}

.service-card-fog {
  background-image: url("https://i.ytimg.com/vi/yu8VjFJvdcQ/maxresdefault.jpg?sqp=-oaymwEmCIAKENAF8quKqQMa8AEB-AH-CYAC0AWKAgwIABABGGUgXChaMA8=&rs=AOn4CLChSVQ3xfIoyTOtEB33I_8ySEfTdw") !important;
}

.service-card-baby-cart {
  background-image: url("https://storage.googleapis.com/shy-pub/337348/1704680813127_SKU-0935_1.jpg") !important;
}

.service-card-mirror-ramp {
  background-image: url("https://images.shaadisaga.com/shaadisaga_production/photos/pictures/003/919/901/new_medium/purple_tree_events.jpg?1653031302") !important;
}

.service-card-gift-hampers {
  background-image: url("https://dispozable.in/cdn/shop/files/2jarpeacock.jpg?v=1754486162") !important;
}

.service-card-destination {
  background-image: url("https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcTdakcGAj7X85-IV6JjKk8t2lzoVxDE0uGu5Q&s") !important;
}

.service-card-corporate {
  background-image: url("https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcSBmGUlgwb-DwtVArJXosBz6icsptWpG4rGMQ&s") !important;
}

.service-card-wedding {
  background-image: url("https://www.grandweddings.co.in/wp-content/uploads/2020/03/indian-wedding-stage-decoration-photos-magnificent-portrait-7-1170x777.jpg") !important;
}

.service-card-naming-ceremony {
  background-image: url("https://cdn.togetherv.com/baby-girl-naming-ceremony-decor-1_1678082817.webp") !important;
}

/*--------------------------------------------------------------
# Hero Section
--------------------------------------------------------------*/
.hero {
  position: relative;
  min-height: 100vh;
  display: flex;
  align-items: center;
  background-image: url("../img/img.jpg");
  background-size: cover;
  background-position: center;
  background-attachment: fixed;
}

@media (max-width: 768px) {
  .hero {
    background-attachment: scroll;
    min-height: 90vh;
  }
}

.hero .background-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    135deg,
    color-mix(in srgb, var(--background-color), transparent 30%) 0%,
    color-mix(in srgb, var(--background-color), transparent 50%) 100%
  );
  z-index: 1;
}

.hero .hero-content {
  position: relative;
  z-index: 2;
  width: 100%;
  padding: 120px 0 10px;
}

@media (max-width: 768px) {
  .hero .hero-content {
    /* padding: 100px 0 60px; */
  }
}

.hero .hero-text {
  margin-bottom: 50px;
}

@media (max-width: 768px) {
  .hero .hero-text {
    margin-bottom: 40px;
  }
}

.hero .hero-title {
  font-size: 3.5rem;
  font-weight: 600;
  letter-spacing: -0.02em;
  line-height: 1.2;
  margin-bottom: 24px;
}

@media (max-width: 1200px) {
  .hero .hero-title {
    font-size: 3rem;
  }
}

@media (max-width: 768px) {
  .hero .hero-title {
    font-size: 2.2rem;
    margin-bottom: 20px;
  }
}

.hero .hero-subtitle {
  font-size: 1.3rem;
  line-height: 1.6;
  margin-bottom: 40px;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

@media (max-width: 768px) {
  .hero .hero-subtitle {
    font-size: 1.1rem;
    margin-bottom: 30px;
  }
}

.hero .event-details {
  display: flex;
  justify-content: center;
  gap: 40px;
  margin-bottom: 20px;
}

@media (max-width: 768px) {
  .hero .event-details {
    flex-direction: column;
    gap: 16px;
    align-items: center;
  }
}

.hero .detail-item {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 1.1rem;
  font-weight: 500;
}

.hero .detail-item i {
  font-size: 1.2rem;
  color: var(--accent-color);
}

@media (max-width: 768px) {
  .hero .detail-item {
    font-size: 1rem;
  }
}

.hero .countdown-section {
  margin-bottom: 50px;
}

@media (max-width: 768px) {
  .hero .countdown-section {
    margin-bottom: 40px;
  }
}

.hero .countdown-label {
  font-size: 1.2rem;
  font-weight: 500;
  margin-bottom: 30px;
  letter-spacing: 0.5px;
  text-transform: uppercase;
}

@media (max-width: 768px) {
  .hero .countdown-label {
    font-size: 1rem;
    margin-bottom: 24px;
  }
}

.hero .countdown {
  gap: 30px;
  margin-bottom: 0;
}

@media (max-width: 768px) {
  .hero .countdown {
    gap: 20px;
  }
}

.hero .countdown div {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  padding: 24px 20px;
  min-width: 120px;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.hero .countdown div:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateY(-2px);
}

@media (max-width: 768px) {
  .hero .countdown div {
    padding: 16px 12px;
    min-width: 90px;
  }
}

.hero .countdown div h3 {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 8px;
  line-height: 1;
}

@media (max-width: 768px) {
  .hero .countdown div h3 {
    font-size: 2rem;
  }
}

.hero .countdown div h4 {
  font-size: 0.9rem;
  color: color-mix(in srgb, var(--default-color) 90%, white 15%);
  margin: 0;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 1px;
}

@media (max-width: 768px) {
  .hero .countdown div h4 {
    font-size: 0.8rem;
  }
}

.hero .cta-section {
  margin-bottom: 60px;
}

@media (max-width: 768px) {
  .hero .cta-section {
    margin-bottom: 50px;
  }
}

.hero .cta-buttons {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-bottom: 24px;
}

@media (max-width: 576px) {
  .hero .cta-buttons {
    flex-direction: column;
    align-items: center;
    gap: 16px;
  }
}

.hero .btn-cta {
  padding: 16px 32px;
  font-size: 1.1rem;
  font-weight: 600;
  letter-spacing: 0.5px;
  border-radius: 8px;
  text-decoration: none;
  transition: all 0.3s ease;
  min-width: 180px;
}

@media (max-width: 768px) {
  .hero .btn-cta {
    padding: 14px 28px;
    font-size: 1rem;
    min-width: 160px;
  }
}

.hero .btn-cta.btn-primary {
  color: var(--contrast-color);
  background-color: var(--accent-color);
  border-color: var(--accent-color);
}

.hero .btn-cta.btn-primary:hover {
  transform: translateY(-2px);
}

.hero .btn-cta.btn-secondary {
  background: transparent;
  border: 2px solid var(--default-color);
  color: var(--default-color);
}

.hero .btn-cta.btn-secondary:hover {
  transform: translateY(-2px);
}

.hero .cta-note {
  color: color-mix(in srgb, var(--contrast-color), transparent 30%);
  font-size: 0.95rem;
  font-style: italic;
  margin: 0;
}

@media (max-width: 768px) {
  .hero .cta-note {
    font-size: 0.9rem;
  }
}

.hero .sponsors-section {
  border-top: 1px solid rgba(255, 255, 255, 0.15);
  padding-top: 40px;
}

@media (max-width: 768px) {
  .hero .sponsors-section {
    padding-top: 30px;
  }
}

.hero .sponsors-label {
  color: color-mix(in srgb, var(--contrast-color), transparent 25%);
  font-size: 0.9rem;
  margin-bottom: 30px;
  text-transform: uppercase;
  letter-spacing: 1px;
  font-weight: 500;
}

@media (max-width: 768px) {
  .hero .sponsors-label {
    font-size: 0.85rem;
    margin-bottom: 24px;
  }
}

.hero .sponsors-logos {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 40px;
  flex-wrap: wrap;
}

@media (max-width: 768px) {
  .hero .sponsors-logos {
    gap: 24px;
  }
}

.hero .sponsor-logo {
  height: 50px;
  width: auto;
  filter: brightness(0) invert(1);
  opacity: 0.7;
  transition: all 0.3s ease;
}

.hero .sponsor-logo:hover {
  opacity: 1;
  transform: scale(1.1);
}

@media (max-width: 768px) {
  .hero .sponsor-logo {
    height: 40px;
  }
}

/*--------------------------------------------------------------
# Intro Section
--------------------------------------------------------------*/
.intro {
  position: relative;
  /* background: linear-gradient(
    135deg,
    var(--background-color) 0%,
    color-mix(in srgb, var(--accent-color), transparent 90%) 100%
  ); */
  overflow: hidden;
}

.intro::before {
  content: "";
  position: absolute;
  top: -50%;
  right: -20%;
  width: 40%;
  height: 200%;
  background: linear-gradient(
    45deg,
    color-mix(in srgb, var(--accent-color), transparent 95%) 0%,
    transparent 70%
  );
  transform: rotate(-15deg);
  z-index: 1;
}

.intro .container {
  position: relative;
  z-index: 2;
}

.intro .content h2 {
  font-size: 3.2rem;
  font-weight: 800;
  margin-bottom: 2rem;
  background-clip: text;
  line-height: 1.2;
}

@media (max-width: 768px) {
  .intro .content h2 {
    font-size: 2.5rem;
  }
}

.intro .content .lead {
  font-size: 1.25rem;
  font-weight: 500;
  color: color-mix(in srgb, var(--default-color), transparent 20%);
  margin-bottom: 1.5rem;
  line-height: 1.6;
}

.intro .content p {
  font-size: 1.1rem;
  line-height: 1.8;
  margin-bottom: 2.5rem;
  color: color-mix(in srgb, var(--default-color), transparent 30%);
}

.intro .stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
  padding: 2rem 0;
}

.intro .stat-item {
  text-align: center;
  position: relative;
}

.intro .stat-item .stat-number {
  font-size: 3rem;
  font-weight: 900;
  color: var(--accent-color);
  line-height: 1;
  margin-bottom: 0.5rem;
  text-shadow: 0 4px 8px
    color-mix(in srgb, var(--accent-color), transparent 70%);
}

.intro .stat-item .stat-label {
  font-size: 0.9rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  color: var(--heading-color);
}

.intro .stat-item::after {
  content: "";
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 30px;
  height: 3px;
  background: linear-gradient(
    90deg,
    var(--accent-color),
    color-mix(in srgb, var(--accent-color), transparent 70%)
  );
  border-radius: 2px;
}

.intro .cta-section {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.intro .cta-section .btn {
  padding: 1rem 2rem;
  display: flex;
  align-items: center;
  font-weight: 600;
  border-radius: 50px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
  box-shadow: 0 8px 25px
    color-mix(in srgb, var(--accent-color), transparent 75%);
}

.intro .cta-section .btn.btn-primary {
  background: linear-gradient(
    135deg,
    var(--accent-color),
    color-mix(in srgb, var(--accent-color), #000000 20%)
  );
  border: none;
  color: var(--contrast-color);
}

.intro .cta-section .btn.btn-primary:hover {
  transform: translateY(-3px);
  box-shadow: 0 12px 35px
    color-mix(in srgb, var(--accent-color), transparent 60%);
}

.intro .cta-section .btn.btn-outline {
  background: transparent;
  border: 2px solid var(--accent-color);
  color: var(--accent-color);
}

.intro .cta-section .btn.btn-outline:hover {
  background: var(--accent-color);
  color: var(--contrast-color);
  transform: translateY(-3px);
}

.intro .visual-section {
  position: relative;
}

.intro .image-wrapper {
  position: relative;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 20px 60px
    color-mix(in srgb, var(--default-color), transparent 85%);
  transform: perspective(1000px) rotateY(-5deg) rotateX(2deg);
  transition: all 0.3s ease;
}

.intro .image-wrapper:hover {
  transform: perspective(1000px) rotateY(0deg) rotateX(0deg);
}

.intro .image-wrapper img {
  width: 100%;
  height: 400px;
  object-fit: cover;
}

.intro .image-wrapper .gradient-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    color-mix(in srgb, var(--accent-color), transparent 60%) 0%,
    transparent 100%
  );
}

.intro .image-wrapper .floating-badge {
  position: absolute;
  top: 20px;
  right: 20px;
  background: linear-gradient(
    135deg,
    var(--accent-color),
    color-mix(in srgb, var(--accent-color), #000000 20%)
  );
  color: var(--contrast-color);
  padding: 0.75rem 1.5rem;
  border-radius: 50px;
  font-weight: 600;
  box-shadow: 0 8px 25px
    color-mix(in srgb, var(--accent-color), transparent 70%);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.intro .image-wrapper .floating-badge i {
  font-size: 1.1rem;
}

.intro .highlight-cards {
  margin-top: 2rem;
  display: flex;
  gap: 1rem;
}

@media (max-width: 768px) {
  .intro .highlight-cards {
    flex-direction: column;
  }
}

.intro .highlight-card {
  flex: 1;
  background: var(--surface-color);
  padding: 1.5rem;
  border-radius: 15px;
  text-align: center;
  box-shadow: 0 10px 30px
    color-mix(in srgb, var(--default-color), transparent 90%);
  border: 1px solid color-mix(in srgb, var(--accent-color), transparent 90%);
  transition: all 0.3s ease;
}

.intro .highlight-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 40px
    color-mix(in srgb, var(--default-color), transparent 85%);
}

.intro .highlight-card i {
  font-size: 2.5rem;
  color: var(--accent-color);
  margin-bottom: 1rem;
  background: linear-gradient(
    135deg,
    var(--accent-color),
    color-mix(in srgb, var(--accent-color), #000000 30%)
  );
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.intro .highlight-card h4 {
  font-size: 1.2rem;
  font-weight: 700;
  margin-bottom: 0.75rem;
}

.intro .highlight-card p {
  font-size: 0.95rem;
  color: color-mix(in srgb, var(--default-color), transparent 40%);
  margin: 0;
  line-height: 1.6;
}

.intro .founder-quote {
  margin-top: 4rem;
  padding: 3rem 2rem;
  background: linear-gradient(
    135deg,
    var(--surface-color) 0%,
    color-mix(in srgb, var(--accent-color), transparent 97%) 100%
  );
  border-radius: 25px;
  border: 1px solid color-mix(in srgb, var(--accent-color), transparent 85%);
  position: relative;
  overflow: hidden;
}

.intro .founder-quote::before {
  content: '"';
  position: absolute;
  top: -20px;
  left: 30px;
  font-size: 8rem;
  font-family: Georgia, serif;
  color: color-mix(in srgb, var(--accent-color), transparent 85%);
  font-weight: bold;
  line-height: 1;
}

.intro .founder-quote .founder-img {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  object-fit: cover;
  border: 4px solid var(--accent-color);
  box-shadow: 0 10px 30px
    color-mix(in srgb, var(--accent-color), transparent 75%);
}

.intro .founder-quote blockquote {
  margin: 0;
}

.intro .founder-quote blockquote p {
  font-size: 1.3rem;
  font-style: italic;
  line-height: 1.7;
  color: var(--heading-color);
  margin-bottom: 1.5rem;
  position: relative;
  z-index: 2;
}

@media (max-width: 768px) {
  .intro .founder-quote blockquote p {
    font-size: 1.1rem;
  }
}

.intro .founder-quote blockquote cite {
  display: block;
  text-align: left;
}

.intro .founder-quote blockquote cite strong {
  display: block;
  font-size: 1.2rem;
  font-weight: 700;
  color: var(--heading-color);
  margin-bottom: 0.25rem;
}

.intro .founder-quote blockquote cite span {
  color: var(--accent-color);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-size: 0.9rem;
}

@media (max-width: 992px) {
  .intro .founder-quote {
    text-align: center;
  }

  .intro .founder-quote .founder-img {
    margin-bottom: 2rem;
  }

  .intro .founder-quote blockquote cite {
    text-align: center;
  }
}

@media (max-width: 768px) {
  .intro .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }

  .intro .cta-section {
    justify-content: center;
  }

  .intro .cta-section .btn {
    flex: 1;
    text-align: center;
  }
}

/*--------------------------------------------------------------
# Featured Speakers Section
--------------------------------------------------------------*/
.featured-speakers .speaker-card {
  background: var(--surface-color);
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 5px 30px 0
    color-mix(in srgb, var(--default-color), transparent 90%);
  transition: all 0.3s ease;
}

.featured-speakers .speaker-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 45px 0
    color-mix(in srgb, var(--default-color), transparent 80%);
}

.featured-speakers .speaker-card.featured {
  background: var(--surface-color);
  border: 2px solid color-mix(in srgb, var(--accent-color), transparent 80%);
}

.featured-speakers .speaker-card.featured .speaker-image {
  position: relative;
  overflow: hidden;
  height: 340px;
}

.featured-speakers .speaker-card.featured .speaker-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: all 0.3s ease;
}

.featured-speakers .speaker-card.featured .speaker-image .speaker-social {
  position: absolute;
  bottom: 20px;
  right: 20px;
  display: flex;
  gap: 10px;
}

.featured-speakers .speaker-card.featured .speaker-image .speaker-social a {
  width: 40px;
  height: 40px;
  background: var(--accent-color);
  color: var(--contrast-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
  transition: all 0.3s ease;
}

.featured-speakers
  .speaker-card.featured
  .speaker-image
  .speaker-social
  a:hover {
  background: var(--heading-color);
  transform: translateY(-3px);
}

.featured-speakers .speaker-card.featured .speaker-image .speaker-social a i {
  font-size: 16px;
}

.featured-speakers .speaker-card.featured .speaker-content {
  padding: 20px;
}

.featured-speakers .speaker-card.featured .speaker-content .speaker-category {
  display: inline-block;
  background: var(--accent-color);
  color: var(--contrast-color);
  padding: 6px 15px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
  margin-bottom: 15px;
}

.featured-speakers .speaker-card.featured .speaker-content .speaker-name {
  font-size: 24px;
  font-weight: 700;
  color: var(--heading-color);
  margin-bottom: 8px;
  line-height: 1.3;
}

.featured-speakers .speaker-card.featured .speaker-content .speaker-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--accent-color);
  margin-bottom: 5px;
}

.featured-speakers .speaker-card.featured .speaker-content .speaker-company {
  font-size: 14px;
  color: color-mix(in srgb, var(--default-color), transparent 30%);
  margin-bottom: 20px;
}

.featured-speakers .speaker-card.featured .speaker-content .speaker-session {
  margin-bottom: 25px;
}

.featured-speakers .speaker-card.featured .speaker-content .speaker-session h4 {
  font-size: 16px;
  font-weight: 600;
  color: var(--heading-color);
  margin-bottom: 10px;
}

.featured-speakers .speaker-card.featured .speaker-content .speaker-session p {
  font-size: 14px;
  line-height: 1.6;
  color: color-mix(in srgb, var(--default-color), transparent 20%);
  margin: 0;
}

.featured-speakers .speaker-card.featured .speaker-content .btn-speaker {
  display: inline-block;
  background: color-mix(in srgb, var(--accent-color), transparent 90%);
  color: var(--accent-color);
  padding: 12px 25px;
  border-radius: 25px;
  text-decoration: none;
  font-weight: 600;
  font-size: 14px;
  transition: all 0.3s ease;
}

.featured-speakers .speaker-card.featured .speaker-content .btn-speaker:hover {
  background: var(--accent-color);
  color: var(--contrast-color);
  transform: translateY(-2px);
}

@media (max-width: 768px) {
  .featured-speakers .speaker-card.featured .speaker-image {
    height: 335px;
  }

  .featured-speakers .speaker-card.featured .speaker-content {
    padding: 20px;
  }

  .featured-speakers .speaker-card.featured .speaker-content .speaker-name {
    font-size: 20px;
  }
}

.featured-speakers .speaker-card.compact {
  text-align: center;
  transition: all 0.3s ease;
}

.featured-speakers .speaker-card.compact .speaker-image {
  position: relative;
  overflow: hidden;
  height: 280px;
}

.featured-speakers .speaker-card.compact .speaker-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: all 0.3s ease;
}

.featured-speakers .speaker-card.compact .speaker-image .speaker-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    180deg,
    transparent 60%,
    color-mix(in srgb, var(--default-color), transparent 30%) 100%
  );
  opacity: 0;
  transition: all 0.3s ease;
  display: flex;
  align-items: flex-end;
  justify-content: center;
  padding: 20px;
}

.featured-speakers
  .speaker-card.compact
  .speaker-image
  .speaker-overlay
  .speaker-social {
  display: flex;
  gap: 10px;
  transform: translateY(20px);
  transition: all 0.3s ease;
}

.featured-speakers
  .speaker-card.compact
  .speaker-image
  .speaker-overlay
  .speaker-social
  a {
  width: 36px;
  height: 36px;
  background: var(--contrast-color);
  color: var(--accent-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
  transition: all 0.3s ease;
}

.featured-speakers
  .speaker-card.compact
  .speaker-image
  .speaker-overlay
  .speaker-social
  a:hover {
  background: var(--accent-color);
  color: var(--contrast-color);
}

.featured-speakers
  .speaker-card.compact
  .speaker-image
  .speaker-overlay
  .speaker-social
  a
  i {
  font-size: 14px;
}

.featured-speakers .speaker-card.compact .speaker-image:hover .speaker-overlay {
  opacity: 1;
}

.featured-speakers
  .speaker-card.compact
  .speaker-image:hover
  .speaker-overlay
  .speaker-social {
  transform: translateY(0);
}

.featured-speakers .speaker-card.compact .speaker-image:hover img {
  transform: scale(1.05);
}

.featured-speakers .speaker-card.compact .speaker-content {
  padding: 25px 20px;
}

.featured-speakers .speaker-card.compact .speaker-content .speaker-name {
  font-size: 18px;
  font-weight: 700;
  color: var(--heading-color);
  margin-bottom: 8px;
  line-height: 1.3;
}

.featured-speakers .speaker-card.compact .speaker-content .speaker-title {
  font-size: 14px;
  font-weight: 600;
  color: var(--accent-color);
  margin-bottom: 5px;
}

.featured-speakers .speaker-card.compact .speaker-content .speaker-company {
  font-size: 13px;
  color: color-mix(in srgb, var(--default-color), transparent 30%);
  margin-bottom: 15px;
}

.featured-speakers .speaker-card.compact .speaker-content .speaker-topic {
  display: inline-block;
  background: color-mix(in srgb, var(--accent-color), transparent 90%);
  color: var(--accent-color);
  padding: 6px 12px;
  border-radius: 15px;
  font-size: 12px;
  font-weight: 500;
  font-style: italic;
}

@media (max-width: 768px) {
  .featured-speakers .speaker-card.compact .speaker-image {
    height: 428px;
  }

  .featured-speakers .speaker-card.compact .speaker-content {
    padding: 20px 15px;
  }

  .featured-speakers .speaker-card.compact .speaker-content .speaker-name {
    font-size: 16px;
  }
}

.featured-speakers .speaker-quote {
  background: color-mix(in srgb, var(--accent-color), transparent 95%);
  padding: 40px;
  border-radius: 15px;
  text-align: center;
  position: relative;
}

.featured-speakers .speaker-quote::before {
  content: '"';
  position: absolute;
  top: -10px;
  left: 50%;
  transform: translateX(-50%);
  font-size: 80px;
  color: var(--accent-color);
  font-family: serif;
  line-height: 1;
  opacity: 0.3;
}

.featured-speakers .speaker-quote blockquote {
  margin: 0;
  position: relative;
  z-index: 1;
}

.featured-speakers .speaker-quote blockquote p {
  font-size: 18px;
  line-height: 1.6;
  color: var(--default-color);
  font-style: italic;
  margin-bottom: 25px;
}

.featured-speakers .speaker-quote .quote-author {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15px;
  position: relative;
  z-index: 1;
}

.featured-speakers .speaker-quote .quote-author img {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  object-fit: cover;
  border: 3px solid var(--accent-color);
}

.featured-speakers .speaker-quote .quote-author .author-info {
  text-align: left;
}

.featured-speakers .speaker-quote .quote-author .author-info h5 {
  font-size: 16px;
  font-weight: 700;
  color: var(--heading-color);
  margin: 0;
  line-height: 1.3;
}

.featured-speakers .speaker-quote .quote-author .author-info span {
  font-size: 14px;
  color: color-mix(in srgb, var(--default-color), transparent 30%);
  line-height: 1.4;
}

@media (max-width: 768px) {
  .featured-speakers .speaker-quote {
    padding: 30px 20px;
  }

  .featured-speakers .speaker-quote::before {
    font-size: 60px;
    top: -5px;
  }

  .featured-speakers .speaker-quote blockquote p {
    font-size: 16px;
  }

  .featured-speakers .speaker-quote .quote-author {
    flex-direction: column;
    text-align: center;
  }

  .featured-speakers .speaker-quote .quote-author .author-info {
    text-align: center;
  }
}

/*--------------------------------------------------------------
# Schedule Section
--------------------------------------------------------------*/
.schedule .schedule-tabs .nav-pills .nav-item {
  margin: 0 8px;
}

.schedule .schedule-tabs .nav-pills .nav-item .nav-link {
  background: transparent;
  border: 2px solid color-mix(in srgb, var(--default-color), transparent 80%);
  color: var(--default-color);
  padding: 12px 24px;
  border-radius: 50px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.schedule .schedule-tabs .nav-pills .nav-item .nav-link:hover {
  border-color: var(--accent-color);
  color: var(--accent-color);
  transform: translateY(-2px);
}

.schedule .schedule-tabs .nav-pills .nav-item .nav-link.active {
  background: var(--accent-color);
  border-color: var(--accent-color);
  color: var(--contrast-color);
  transform: translateY(-2px);
}

.schedule .track-headers {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 20px;
  margin-bottom: 40px;
  padding: 0 60px;
}

.schedule .track-headers .track-header {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 15px;
  border-radius: 12px;
  font-weight: 600;
  color: var(--contrast-color);
}

.schedule .track-headers .track-header i {
  margin-right: 8px;
  font-size: 18px;
}

.schedule .track-headers .track-header.development {
  background: linear-gradient(135deg, #4f46e5, #7c3aed);
}

.schedule .track-headers .track-header.design {
  background: linear-gradient(135deg, #f59e0b, #ef4444);
}

.schedule .track-headers .track-header.business {
  background: linear-gradient(135deg, #059669, #0891b2);
}

@media (max-width: 768px) {
  .schedule .track-headers {
    grid-template-columns: 1fr;
    padding: 0;
  }
}

.schedule .schedule-timeline .time-slot {
  display: grid;
  grid-template-columns: 120px 1fr;
  gap: 40px;
  margin-bottom: 40px;
  align-items: start;
}

.schedule .schedule-timeline .time-slot .time-label {
  text-align: right;
  padding-top: 20px;
  position: sticky;
  top: 100px;
}

.schedule .schedule-timeline .time-slot .time-label .time {
  display: block;
  font-size: 18px;
  font-weight: 700;
  color: var(--heading-color);
}

.schedule .schedule-timeline .time-slot .time-label .duration {
  display: block;
  font-size: 14px;
  color: color-mix(in srgb, var(--default-color), transparent 40%);
  margin-top: 4px;
}

.schedule .schedule-timeline .time-slot .sessions-row {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 20px;
}

.schedule .schedule-timeline .time-slot .sessions-row .session-card {
  background: var(--surface-color);
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 20px
    color-mix(in srgb, var(--default-color), transparent 90%);
  transition: all 0.3s ease;
  border-left: 4px solid transparent;
}

.schedule .schedule-timeline .time-slot .sessions-row .session-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 30px
    color-mix(in srgb, var(--default-color), transparent 85%);
}

.schedule .schedule-timeline .time-slot .sessions-row .session-card.keynote {
  grid-column: 1/-1;
  border-left-color: #fbbf24;
  background: linear-gradient(
    135deg,
    var(--surface-color),
    color-mix(in srgb, #fbbf24, transparent 95%)
  );
}

.schedule
  .schedule-timeline
  .time-slot
  .sessions-row
  .session-card.keynote
  .session-type {
  color: #f59e0b;
}

.schedule
  .schedule-timeline
  .time-slot
  .sessions-row
  .session-card.development {
  border-left-color: #4f46e5;
}

.schedule
  .schedule-timeline
  .time-slot
  .sessions-row
  .session-card.development
  .session-type {
  color: #4f46e5;
}

.schedule .schedule-timeline .time-slot .sessions-row .session-card.design {
  border-left-color: #ef4444;
}

.schedule
  .schedule-timeline
  .time-slot
  .sessions-row
  .session-card.design
  .session-type {
  color: #ef4444;
}

.schedule .schedule-timeline .time-slot .sessions-row .session-card.business {
  border-left-color: #059669;
}

.schedule
  .schedule-timeline
  .time-slot
  .sessions-row
  .session-card.business
  .session-type {
  color: #059669;
}

.schedule .schedule-timeline .time-slot .sessions-row .session-card.break {
  grid-column: 1/-1;
  border-left-color: #6b7280;
  background: color-mix(in srgb, var(--default-color), transparent 95%);
}

.schedule
  .schedule-timeline
  .time-slot
  .sessions-row
  .session-card.break
  .session-type {
  color: #6b7280;
}

.schedule .schedule-timeline .time-slot .sessions-row .session-card.break h4 {
  color: color-mix(in srgb, var(--default-color), transparent 30%);
}

.schedule .schedule-timeline .time-slot .sessions-row .session-card.workshop {
  grid-column: 1/-1;
  border-left-color: #8b5cf6;
  background: linear-gradient(
    135deg,
    var(--surface-color),
    color-mix(in srgb, #8b5cf6, transparent 95%)
  );
}

.schedule
  .schedule-timeline
  .time-slot
  .sessions-row
  .session-card.workshop
  .session-type {
  color: #8b5cf6;
}

.schedule
  .schedule-timeline
  .time-slot
  .sessions-row
  .session-card
  .session-type {
  display: flex;
  align-items: center;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 12px;
}

.schedule
  .schedule-timeline
  .time-slot
  .sessions-row
  .session-card
  .session-type
  i {
  margin-right: 6px;
  font-size: 14px;
}

.schedule .schedule-timeline .time-slot .sessions-row .session-card h4 {
  font-size: 18px;
  font-weight: 700;
  color: var(--heading-color);
  margin-bottom: 16px;
  line-height: 1.4;
}

.schedule .schedule-timeline .time-slot .sessions-row .session-card .speaker {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.schedule
  .schedule-timeline
  .time-slot
  .sessions-row
  .session-card
  .speaker
  .speaker-image {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  object-fit: cover;
  margin-right: 12px;
}

.schedule
  .schedule-timeline
  .time-slot
  .sessions-row
  .session-card
  .speaker
  .speaker-details
  h5 {
  font-size: 14px;
  font-weight: 600;
  color: var(--heading-color);
  margin: 0;
}

.schedule
  .schedule-timeline
  .time-slot
  .sessions-row
  .session-card
  .speaker
  .speaker-details
  span {
  font-size: 12px;
  color: color-mix(in srgb, var(--default-color), transparent 40%);
}

.schedule .schedule-timeline .time-slot .sessions-row .session-card p {
  font-size: 14px;
  color: color-mix(in srgb, var(--default-color), transparent 20%);
  margin-bottom: 16px;
  line-height: 1.5;
}

.schedule
  .schedule-timeline
  .time-slot
  .sessions-row
  .session-card
  .session-meta {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 12px;
}

.schedule
  .schedule-timeline
  .time-slot
  .sessions-row
  .session-card
  .session-meta
  .venue {
  font-size: 12px;
  color: color-mix(in srgb, var(--default-color), transparent 40%);
  display: flex;
  align-items: center;
}

.schedule
  .schedule-timeline
  .time-slot
  .sessions-row
  .session-card
  .session-meta
  .venue:before {
  content: "\f3c5";
  font-family: "bootstrap-icons";
  margin-right: 4px;
}

.schedule
  .schedule-timeline
  .time-slot
  .sessions-row
  .session-card
  .session-meta
  .level {
  font-size: 11px;
  padding: 4px 8px;
  border-radius: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.schedule
  .schedule-timeline
  .time-slot
  .sessions-row
  .session-card
  .session-meta
  .level.beginner {
  background: color-mix(in srgb, #10b981, transparent 85%);
  color: #059669;
}

.schedule
  .schedule-timeline
  .time-slot
  .sessions-row
  .session-card
  .session-meta
  .level.intermediate {
  background: color-mix(in srgb, #f59e0b, transparent 85%);
  color: #d97706;
}

.schedule
  .schedule-timeline
  .time-slot
  .sessions-row
  .session-card
  .session-meta
  .level.advanced {
  background: color-mix(in srgb, #ef4444, transparent 85%);
  color: #dc2626;
}

.schedule .schedule-timeline .time-slot.break-slot .sessions-row {
  grid-template-columns: 1fr;
}

@media (max-width: 992px) {
  .schedule .schedule-timeline .time-slot .sessions-row {
    grid-template-columns: 1fr;
  }

  .schedule .schedule-timeline .time-slot .sessions-row .session-card.keynote,
  .schedule .schedule-timeline .time-slot .sessions-row .session-card.workshop {
    grid-column: 1;
  }
}

@media (max-width: 768px) {
  .schedule .schedule-timeline .time-slot {
    grid-template-columns: 80px 1fr;
    gap: 20px;
  }

  .schedule .schedule-timeline .time-slot .time-label {
    text-align: left;
  }

  .schedule .schedule-timeline .time-slot .time-label .time {
    font-size: 16px;
  }
}

.schedule .download-cta {
  background: var(--surface-color);
  border-radius: 20px;
  padding: 40px;
  margin-top: 60px;
  box-shadow: 0 8px 30px
    color-mix(in srgb, var(--default-color), transparent 90%);
}

.schedule .download-cta h4 {
  font-size: 24px;
  margin-bottom: 12px;
  color: var(--heading-color);
}

.schedule .download-cta p {
  color: color-mix(in srgb, var(--default-color), transparent 30%);
  margin-bottom: 24px;
}

.schedule .download-cta .cta-buttons {
  display: flex;
  gap: 16px;
  justify-content: center;
  flex-wrap: wrap;
}

.schedule .download-cta .cta-buttons .btn {
  padding: 12px 24px;
  border-radius: 50px;
  font-weight: 600;
  display: flex;
  align-items: center;
  transition: all 0.3s ease;
}

.schedule .download-cta .cta-buttons .btn i {
  margin-right: 8px;
}

.schedule .download-cta .cta-buttons .btn.btn-primary {
  background: var(--accent-color);
  border-color: var(--accent-color);
  color: var(--contrast-color);
}

.schedule .download-cta .cta-buttons .btn.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px
    color-mix(in srgb, var(--accent-color), transparent 60%);
}

.schedule .download-cta .cta-buttons .btn.btn-outline-primary {
  border-color: var(--accent-color);
  color: var(--accent-color);
}

.schedule .download-cta .cta-buttons .btn.btn-outline-primary:hover {
  background: var(--accent-color);
  color: var(--contrast-color);
  transform: translateY(-2px);
}

/*--------------------------------------------------------------
# Call To Action Section
--------------------------------------------------------------*/
.call-to-action {
  position: relative;
  overflow: hidden;
}

.call-to-action::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url("../img/events/showcase-1.webp") center/cover no-repeat;
  opacity: 1;
  z-index: 1;
}

.call-to-action .container {
  position: relative;
  z-index: 2;
}

.call-to-action .display-4 {
  font-weight: 600;
  letter-spacing: -0.02em;
  line-height: 1.2;
}

.call-to-action .lead {
  font-size: 1.125rem;
  line-height: 1.6;
  font-weight: 400;
}

.call-to-action .stats-wrapper {
  background: var(--surface-color);
  border-radius: 16px;
  padding: 3rem 2rem;
  backdrop-filter: blur(10px);
  border: 1px solid color-mix(in srgb, var(--default-color), transparent 90%);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.08);
}

.call-to-action .stat-item {
  text-align: center;
  padding: 1rem 0;
}

.call-to-action .stat-item .stat-icon {
  width: 75px;
  height: 75px;
  background: color-mix(in srgb, var(--accent-color), transparent 90%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1rem;
  transition: all 0.3s ease;
}

.call-to-action .stat-item .stat-icon i {
  font-size: 2.5rem;
  color: var(--accent-color);
}

.call-to-action .stat-item .stat-number {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--heading-color);
  line-height: 1;
  margin-bottom: 0.5rem;
}

.call-to-action .stat-item .stat-label {
  font-size: 0.975rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.call-to-action .stat-item:hover .stat-icon {
  background: var(--accent-color);
  transform: translateY(-2px);
}

.call-to-action .stat-item:hover .stat-icon i {
  color: var(--contrast-color);
}

.call-to-action .cta-content h3 {
  font-size: 1.75rem;
  font-weight: 600;
  color: var(--heading-color);
  margin-bottom: 1rem;
}

.call-to-action .cta-content p {
  color: color-mix(in srgb, var(--default-color), transparent 20%);
  font-size: 1.125rem;
  line-height: 1.6;
}

.call-to-action .cta-buttons {
  margin-bottom: 2rem;
}

.call-to-action .cta-buttons .btn {
  border-radius: 50px;
  padding: 0.875rem 2rem;
  font-weight: 600;
  font-size: 1rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  transition: all 0.3s ease;
  border-width: 2px;
}

.call-to-action .cta-buttons .btn.btn-primary {
  background: var(--accent-color);
  border-color: var(--accent-color);
  color: var(--contrast-color);
}

.call-to-action .cta-buttons .btn.btn-primary:hover {
  background: color-mix(in srgb, var(--accent-color), black 15%);
  border-color: color-mix(in srgb, var(--accent-color), black 15%);
  transform: translateY(-2px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
}

.call-to-action .cta-buttons .btn.btn-outline-secondary {
  color: var(--default-color);
  border-color: color-mix(in srgb, var(--default-color), transparent 70%);
  background: transparent;
}

.call-to-action .cta-buttons .btn.btn-outline-secondary:hover {
  background: var(--accent-color);
  border-color: var(--accent-color);
  color: var(--contrast-color);
  transform: translateY(-2px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
}

.call-to-action .security-note {
  border-top: 1px solid
    color-mix(in srgb, var(--default-color), transparent 90%);
  padding-top: 1rem;
}

.call-to-action .security-note small {
  display: inline-flex;
  align-items: center;
  font-size: 0.875rem;
  color: color-mix(in srgb, var(--default-color), transparent 40%);
}

.call-to-action .security-note small i {
  color: var(--accent-color);
}

@media (max-width: 768px) {
  .call-to-action .display-4 {
    font-size: 2rem;
  }

  .call-to-action .stats-wrapper {
    padding: 2rem 1rem;
  }

  .call-to-action .stat-item {
    margin-bottom: 2rem;
  }

  .call-to-action .stat-item .stat-number {
    font-size: 2rem;
  }

  .call-to-action .cta-buttons .btn {
    display: block;
    width: 100%;
    margin-bottom: 1rem;
  }

  .call-to-action .cta-buttons .btn:last-child {
    margin-bottom: 0;
  }
}

/*--------------------------------------------------------------
# Testimonials Section
--------------------------------------------------------------*/
.testimonials {
  position: relative;
  overflow: hidden;
  /* Swiper Navigation */
  /* Swiper Pagination */
  /* Responsive Styles */
}

.testimonials .testimonial-slider {
  position: relative;
  padding-bottom: 50px;
}

.testimonials .testimonial-slider .swiper-wrapper {
  height: auto !important;
}

.testimonials .testimonial-item {
  background: linear-gradient(
    135deg,
    var(--surface-color) 0%,
    color-mix(in srgb, var(--surface-color), var(--accent-color) 2%) 100%
  );
  border-radius: 20px;
  padding: 0;
  height: 100%;
  border: 1px solid color-mix(in srgb, var(--default-color), transparent 90%);
  transition: all 0.4s ease;
  overflow: hidden;
  position: relative;
}

.testimonials .testimonial-item::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(
    90deg,
    var(--accent-color),
    color-mix(in srgb, var(--accent-color), var(--heading-color) 30%)
  );
  transform: scaleX(0);
  transform-origin: left;
  transition: transform 0.4s ease;
}

.testimonials .testimonial-item:hover {
  border-color: var(--accent-color);
}

.testimonials .testimonial-item:hover::before {
  transform: scaleX(1);
}

.testimonials .testimonial-item:hover .testimonial-header img {
  transform: scale(1.05);
}

.testimonials .testimonial-item:hover .quote-icon {
  color: var(--accent-color);
  transform: scale(1.1);
}

.testimonials .testimonial-header {
  position: relative;
  text-align: center;
  padding: 30px 30px 20px;
  background: linear-gradient(
    135deg,
    color-mix(in srgb, var(--surface-color), var(--accent-color) 3%) 0%,
    var(--surface-color) 100%
  );
}

.testimonials .testimonial-header img {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  object-fit: cover;
  border: 4px solid color-mix(in srgb, var(--accent-color), transparent 70%);
  margin-bottom: 15px;
  transition: all 0.3s ease;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.testimonials .testimonial-header .rating {
  display: flex;
  justify-content: center;
  gap: 3px;
}

.testimonials .testimonial-header .rating i {
  color: #ffc107;
  font-size: 0.9rem;
}

.testimonials .testimonial-body {
  padding: 0 30px 20px;
}

.testimonials .testimonial-body p {
  font-size: 1rem;
  line-height: 1.6;
  color: color-mix(in srgb, var(--default-color), transparent 20%);
  margin: 0;
  font-style: italic;
  text-align: center;
  position: relative;
}

.testimonials .testimonial-body p::before,
.testimonials .testimonial-body p::after {
  content: '"';
  font-size: 1.5rem;
  color: var(--accent-color);
  opacity: 0.6;
  font-family: serif;
  position: absolute;
}

.testimonials .testimonial-body p::before {
  top: -5px;
  left: -10px;
}

.testimonials .testimonial-body p::after {
  bottom: -20px;
  right: -5px;
}

.testimonials .testimonial-footer {
  padding: 20px 30px 30px;
  text-align: center;
  position: relative;
}

.testimonials .testimonial-footer h5 {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--heading-color);
  margin: 0 0 5px;
}

.testimonials .testimonial-footer span {
  font-size: 0.85rem;
  color: color-mix(in srgb, var(--default-color), transparent 40%);
  display: block;
  margin-bottom: 15px;
}

.testimonials .testimonial-footer .quote-icon {
  position: absolute;
  bottom: 15px;
  right: 25px;
  color: color-mix(in srgb, var(--accent-color), transparent 60%);
  font-size: 1.5rem;
  transition: all 0.3s ease;
}

.testimonials .swiper-navigation {
  position: relative;
  margin-top: 25px;
  display: flex;
  justify-content: flex-end;
}

.testimonials .swiper-button-prev,
.testimonials .swiper-button-next {
  position: static;
  width: 45px;
  height: 45px;
  margin: 0 10px;
  background: var(--accent-color);
  border-radius: 50%;
  color: var(--contrast-color);
  font-size: 16px;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.testimonials .swiper-button-prev:hover,
.testimonials .swiper-button-next:hover {
  background: color-mix(in srgb, var(--accent-color), var(--heading-color) 20%);
  transform: scale(1.05);
}

.testimonials .swiper-button-prev::after,
.testimonials .swiper-button-next::after {
  font-size: 16px;
  font-weight: 600;
}

.testimonials .swiper-pagination {
  position: static;
  margin-top: 30px;
  text-align: center;
}

.testimonials .swiper-pagination .swiper-pagination-bullet {
  width: 12px;
  height: 12px;
  background: color-mix(in srgb, var(--default-color), transparent 70%);
  opacity: 1;
  margin: 0 6px;
  transition: all 0.3s ease;
}

.testimonials
  .swiper-pagination
  .swiper-pagination-bullet.swiper-pagination-bullet-active {
  background: var(--accent-color);
  transform: scale(1.2);
}

@media (max-width: 1199px) {
  .testimonials .testimonial-item .testimonial-header {
    padding: 25px 25px 15px;
  }

  .testimonials .testimonial-item .testimonial-header img {
    width: 70px;
    height: 70px;
  }

  .testimonials .testimonial-item .testimonial-body,
  .testimonials .testimonial-item .testimonial-footer {
    padding-left: 25px;
    padding-right: 25px;
  }
}

@media (max-width: 991px) {
  .testimonials .testimonial-item {
    margin-bottom: 30px;
  }
}

@media (max-width: 767px) {
  .testimonials .testimonial-item .testimonial-header {
    padding: 20px 20px 10px;
  }

  .testimonials .testimonial-item .testimonial-header img {
    width: 60px;
    height: 60px;
  }

  .testimonials .testimonial-item .testimonial-header .rating i {
    font-size: 0.8rem;
  }

  .testimonials .testimonial-item .testimonial-body {
    padding: 0 20px 15px;
  }

  .testimonials .testimonial-item .testimonial-body p {
    font-size: 0.95rem;
  }

  .testimonials .testimonial-item .testimonial-footer {
    padding: 15px 20px 20px;
  }

  .testimonials .testimonial-item .testimonial-footer h5 {
    font-size: 1rem;
  }

  .testimonials .testimonial-item .testimonial-footer span {
    font-size: 0.8rem;
  }

  .testimonials .testimonial-item .testimonial-footer .quote-icon {
    font-size: 1.3rem;
    bottom: 10px;
    right: 15px;
  }

  .testimonials .swiper-button-prev,
  .testimonials .swiper-button-next {
    width: 40px;
    height: 40px;
    font-size: 14px;
  }

  .testimonials .swiper-button-prev::after,
  .testimonials .swiper-button-next::after {
    font-size: 14px;
  }
}

@media (max-width: 575px) {
  .testimonials .testimonial-slider {
    padding-bottom: 30px;
  }

  .testimonials .testimonial-item .testimonial-header {
    padding: 15px 15px 10px;
  }

  .testimonials .testimonial-item .testimonial-header img {
    width: 55px;
    height: 55px;
  }

  .testimonials .testimonial-item .testimonial-body {
    padding: 0 15px 10px;
  }

  .testimonials .testimonial-item .testimonial-body p {
    font-size: 0.9rem;
  }

  .testimonials .testimonial-item .testimonial-footer {
    padding: 10px 15px 15px;
  }

  .testimonials .testimonial-item .testimonial-footer h5 {
    font-size: 0.95rem;
  }

  .testimonials .testimonial-item .testimonial-footer .quote-icon {
    font-size: 1.2rem;
  }

  .testimonials .swiper-navigation {
    margin-top: 10px;
  }
}

/*--------------------------------------------------------------
# Gallery Section
--------------------------------------------------------------*/
.gallery .section-title h2 {
  font-size: 2.5rem;
  font-weight: 700;
  color: #333;
  margin-bottom: 1rem;
}

.gallery .section-title p {
  color: #666;
  font-size: 1.1rem;
  max-width: 600px;
  margin: 0 auto;
}

/* Filter Buttons */
.gallery-filters {
  margin-bottom: 3rem;
}

.filter-btn {
  background: #f8f9fa;
  border: 2px solid #e9ecef;
  color: #666;
  padding: 12px 24px;
  margin: 0 8px;
  border-radius: 25px;
  font-weight: 500;
  transition: all 0.3s ease;
  cursor: pointer;
  font-size: 14px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.filter-btn:hover,
.filter-btn.active {
  background: #ff6b35;
  border-color: #ff6b35;
  color: #fff;
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(255, 107, 53, 0.3);
}

/* Gallery Grid */
.gallery-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  grid-auto-rows: 250px;
  gap: 20px;
  padding: 0;
}

.gallery-item {
  position: relative;
  overflow: hidden;
  border-radius: 15px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

.gallery-item.large {
  grid-row: span 2;
}

.gallery-item.wide {
  grid-column: span 2;
}

.gallery-item.tall {
  grid-row: span 2;
}

.gallery-item img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.gallery-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: all 0.3s ease;
}

.gallery-overlay a {
  color: #fff;
  font-size: 2rem;
  text-decoration: none;
  transform: scale(0.8);
  transition: transform 0.3s ease;
}

.gallery-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
}

.gallery-item:hover img {
  transform: scale(1.1);
}

.gallery-item:hover .gallery-overlay {
  opacity: 1;
}

.gallery-item:hover .gallery-overlay a {
  transform: scale(1);
}

/* Filter Animation */
.gallery-item {
  animation: fadeIn 0.6s ease;
}

.gallery-item.hide {
  display: none;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .gallery-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    grid-auto-rows: 200px;
    gap: 15px;
  }

  .gallery-item.wide {
    grid-column: span 1;
  }

  .filter-btn {
    padding: 10px 20px;
    margin: 5px;
    font-size: 12px;
  }

  .gallery .section-title h2 {
    font-size: 2rem;
  }
}

@media (max-width: 480px) {
  .gallery-grid {
    grid-template-columns: 1fr;
    grid-auto-rows: 250px;
  }

  .gallery-item.large,
  .gallery-item.tall {
    grid-row: span 1;
  }
}

.glightbox-clean .gslide-description {
  background: #272727;
}

.glightbox-clean .gslide-title {
  color: rgba(255, 255, 255, 0.8);
  margin: 0;
}

/*--------------------------------------------------------------
# About Section
--------------------------------------------------------------*/
.about .content h3 {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
}

.about .content .lead {
  font-size: 1.1rem;
  color: color-mix(in srgb, var(--default-color), transparent 20%);
  margin-bottom: 1.5rem;
}

.about .content p {
  margin-bottom: 1.5rem;
  line-height: 1.7;
}

.about .content .quote-section {
  background-color: var(--surface-color);
  padding: 2rem;
  border-radius: 10px;
  border-left: 4px solid var(--accent-color);
  margin: 2rem 0;
}

.about .content .quote-section blockquote {
  margin: 0;
}

.about .content .quote-section blockquote p {
  font-style: italic;
  font-size: 1.1rem;
  margin-bottom: 1rem;
  color: var(--heading-color);
}

.about .content .quote-section blockquote cite {
  font-style: normal;
  font-weight: 600;
  color: var(--accent-color);
  font-size: 0.9rem;
}

.about .content .cta-buttons {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.about .content .cta-buttons .btn-primary,
.about .content .cta-buttons .btn-secondary {
  padding: 12px 30px;
  border-radius: 25px;
  font-weight: 600;
  text-decoration: none;
  transition: all 0.3s ease;
  border: 2px solid transparent;
  display: inline-block;
}

.about .content .cta-buttons .btn-primary {
  background-color: var(--accent-color);
  color: var(--contrast-color);
}

.about .content .cta-buttons .btn-primary:hover {
  background-color: color-mix(in srgb, var(--accent-color), black 15%);
  transform: translateY(-2px);
}

.about .content .cta-buttons .btn-secondary {
  background-color: transparent;
  color: var(--accent-color);
  border-color: var(--accent-color);
}

.about .content .cta-buttons .btn-secondary:hover {
  background-color: var(--accent-color);
  color: var(--contrast-color);
  transform: translateY(-2px);
}

.about .stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1.5rem;
}

.about .stats-grid .stat-card {
  background-color: var(--surface-color);
  padding: 2rem;
  border-radius: 15px;
  text-align: center;
  box-shadow: 0 5px 15px
    color-mix(in srgb, var(--default-color), transparent 90%);
  transition: all 0.3s ease;
}

.about .stats-grid .stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px
    color-mix(in srgb, var(--default-color), transparent 85%);
}

.about .stats-grid .stat-card .stat-icon {
  margin-bottom: 1rem;
}

.about .stats-grid .stat-card .stat-icon i {
  font-size: 2.5rem;
  color: var(--accent-color);
}

.about .stats-grid .stat-card .stat-content h4 {
  font-size: 1.8rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  color: var(--heading-color);
}

.about .stats-grid .stat-card .stat-content p {
  color: color-mix(in srgb, var(--default-color), transparent 30%);
  margin: 0;
  font-size: 0.9rem;
}

.about .audience-section {
  text-align: center;
}

.about .audience-section h3 {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 1rem;
}

.about .audience-section p {
  color: color-mix(in srgb, var(--default-color), transparent 20%);
  font-size: 1.1rem;
}

.about .audience-section .audience-item {
  text-align: center;
  padding: 1.5rem 1rem;
  border-radius: 10px;
  transition: all 0.3s ease;
}

.about .audience-section .audience-item:hover {
  background-color: var(--surface-color);
  transform: translateY(-5px);
}

.about .audience-section .audience-item:hover .audience-icon i {
  color: var(--accent-color);
  transform: scale(1.1);
}

.about .audience-section .audience-item .audience-icon {
  margin-bottom: 1rem;
}

.about .audience-section .audience-item .audience-icon i {
  font-size: 2rem;
  color: var(--heading-color);
  transition: all 0.3s ease;
}

.about .audience-section .audience-item h5 {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: var(--heading-color);
}

.about .audience-section .audience-item p {
  font-size: 0.85rem;
  color: color-mix(in srgb, var(--default-color), transparent 40%);
  margin: 0;
  line-height: 1.4;
}

@media (max-width: 992px) {
  .about .content {
    margin-bottom: 3rem;
  }

  .about .content .cta-buttons {
    justify-content: center;
  }

  .about .stats-grid {
    grid-template-columns: 1fr;
    max-width: 400px;
    margin: 0 auto;
  }
}

@media (max-width: 768px) {
  .about .content h3 {
    font-size: 1.8rem;
  }

  .about .content .quote-section {
    padding: 1.5rem;
  }

  .about .content .cta-buttons {
    flex-direction: column;
    align-items: center;
  }

  .about .content .cta-buttons .btn-primary,
  .about .content .cta-buttons .btn-secondary {
    width: 100%;
    max-width: 250px;
    text-align: center;
  }

  .about .audience-section .audience-item {
    padding: 1rem 0.5rem;
  }

  .about .audience-section .audience-item .audience-icon i {
    font-size: 1.8rem;
  }

  .about .audience-section .audience-item h5 {
    font-size: 0.9rem;
  }

  .about .audience-section .audience-item p {
    font-size: 0.8rem;
  }
}

/*--------------------------------------------------------------
# Speakers Section
--------------------------------------------------------------*/
.speakers .speakers-list .speaker-entry {
  margin-bottom: 2rem;
}

.speakers .speakers-list .speaker-profile {
  background: var(--surface-color);
  border: 1px solid color-mix(in srgb, var(--default-color), transparent 90%);
  border-radius: 12px;
  padding: 25px;
  transition: all 0.3s ease;
  height: 100%;
}

.speakers .speakers-list .speaker-profile:hover {
  border-color: var(--accent-color);
  box-shadow: 0 8px 30px
    color-mix(in srgb, var(--accent-color), transparent 92%);
  transform: translateY(-3px);
}

.speakers .speakers-list .speaker-profile:hover .speaker-photo img {
  transform: scale(1.05);
}

.speakers .speakers-list .speaker-meta {
  display: flex;
  align-items: flex-start;
  gap: 20px;
  margin-bottom: 20px;
}

.speakers .speakers-list .speaker-photo {
  flex-shrink: 0;
  width: 85px;
  height: 85px;
  border-radius: 50%;
  overflow: hidden;
  border: 3px solid color-mix(in srgb, var(--accent-color), transparent 80%);
}

.speakers .speakers-list .speaker-photo img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.speakers .speakers-list .speaker-info {
  flex-grow: 1;
}

.speakers .speakers-list .speaker-info h4 {
  font-size: 20px;
  font-weight: 700;
  margin-bottom: 6px;
  color: var(--heading-color);
}

.speakers .speakers-list .speaker-info .speaker-position {
  display: block;
  font-weight: 600;
  font-size: 15px;
  color: var(--accent-color);
  margin-bottom: 3px;
}

.speakers .speakers-list .speaker-info .speaker-org {
  display: block;
  font-size: 13px;
  color: color-mix(in srgb, var(--default-color), transparent 40%);
  margin-bottom: 10px;
}

.speakers .speakers-list .speaker-info .speaker-track {
  display: inline-block;
  background: linear-gradient(
    135deg,
    var(--accent-color),
    color-mix(in srgb, var(--accent-color), #000000 15%)
  );
  color: var(--contrast-color);
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  padding: 4px 10px;
  border-radius: 15px;
}

.speakers .speakers-list .speaker-details .speaker-topic {
  display: flex;
  align-items: flex-start;
  gap: 10px;
  margin-bottom: 15px;
  padding: 12px;
  background: color-mix(in srgb, var(--accent-color), transparent 95%);
  border-radius: 8px;
  border-left: 3px solid var(--accent-color);
}

.speakers .speakers-list .speaker-details .speaker-topic i {
  color: var(--accent-color);
  font-size: 16px;
  margin-top: 2px;
  flex-shrink: 0;
}

.speakers .speakers-list .speaker-details .speaker-topic span {
  font-weight: 600;
  font-size: 14px;
  color: var(--heading-color);
  line-height: 1.4;
}

.speakers .speakers-list .speaker-details .speaker-summary {
  font-size: 14px;
  line-height: 1.6;
  color: color-mix(in srgb, var(--default-color), transparent 25%);
  margin-bottom: 20px;
}

.speakers .speakers-list .speaker-details .speaker-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.speakers .speakers-list .speaker-details .speaker-actions .profile-btn {
  background: var(--accent-color);
  color: var(--contrast-color);
  padding: 8px 18px;
  border-radius: 25px;
  font-size: 13px;
  font-weight: 600;
  text-decoration: none;
  transition: all 0.3s ease;
}

.speakers .speakers-list .speaker-details .speaker-actions .profile-btn:hover {
  background: color-mix(in srgb, var(--accent-color), #000000 15%);
  transform: translateY(-2px);
}

.speakers .speakers-list .speaker-details .speaker-actions .speaker-social {
  display: flex;
  gap: 8px;
}

.speakers
  .speakers-list
  .speaker-details
  .speaker-actions
  .speaker-social
  .social-link {
  width: 32px;
  height: 32px;
  border: 1px solid color-mix(in srgb, var(--default-color), transparent 80%);
  color: var(--default-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  transition: all 0.3s ease;
}

.speakers
  .speakers-list
  .speaker-details
  .speaker-actions
  .speaker-social
  .social-link:hover {
  border-color: var(--accent-color);
  background: var(--accent-color);
  color: var(--contrast-color);
  transform: translateY(-2px);
}

@media (max-width: 992px) {
  .speakers .speakers-list .speaker-profile {
    padding: 20px;
  }

  .speakers .speakers-list .speaker-meta {
    gap: 15px;
  }

  .speakers .speakers-list .speaker-photo {
    width: 75px;
    height: 75px;
  }

  .speakers .speakers-list .speaker-info h4 {
    font-size: 18px;
  }
}

@media (max-width: 768px) {
  .speakers .speakers-list .speaker-entry {
    margin-bottom: 1.5rem;
  }

  .speakers .speakers-list .speaker-profile {
    padding: 18px;
  }

  .speakers .speakers-list .speaker-meta {
    flex-direction: column;
    align-items: center;
    text-align: center;
    gap: 15px;
  }

  .speakers .speakers-list .speaker-photo {
    width: 80px;
    height: 80px;
  }

  .speakers .speakers-list .speaker-details .speaker-actions {
    flex-direction: column;
    gap: 15px;
  }

  .speakers .speakers-list .speaker-details .speaker-actions .profile-btn {
    align-self: stretch;
    text-align: center;
  }
}

@media (max-width: 576px) {
  .speakers .speakers-list .speaker-profile {
    padding: 15px;
  }

  .speakers .speakers-list .speaker-info h4 {
    font-size: 17px;
  }

  .speakers .speakers-list .speaker-info .speaker-position {
    font-size: 14px;
  }

  .speakers .speakers-list .speaker-details .speaker-topic {
    padding: 10px;
  }

  .speakers .speakers-list .speaker-details .speaker-topic span {
    font-size: 13px;
  }

  .speakers .speakers-list .speaker-details .speaker-summary {
    font-size: 13px;
  }
}

/*--------------------------------------------------------------
# Speaker Details 2 Section
--------------------------------------------------------------*/
.speaker-details-2 .speaker-profile-card {
  background: var(--surface-color);
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 15px 40px
    color-mix(in srgb, var(--default-color), transparent 90%);
  margin-bottom: 2rem;
}

.speaker-details-2 .speaker-profile-card .profile-image-wrapper {
  position: relative;
}

.speaker-details-2 .speaker-profile-card .profile-image-wrapper img {
  width: 100%;
  height: 320px;
  object-fit: cover;
}

@media (max-width: 768px) {
  .speaker-details-2 .speaker-profile-card .profile-image-wrapper img {
    height: 280px;
  }
}

.speaker-details-2
  .speaker-profile-card
  .profile-image-wrapper
  .speaker-status {
  position: absolute;
  top: 1rem;
  right: 1rem;
}

.speaker-details-2
  .speaker-profile-card
  .profile-image-wrapper
  .speaker-status
  .status-badge {
  background: var(--accent-color);
  color: var(--contrast-color);
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.speaker-details-2
  .speaker-profile-card
  .profile-image-wrapper
  .speaker-status
  .status-badge.keynote {
  background: linear-gradient(
    135deg,
    var(--accent-color),
    color-mix(in srgb, var(--accent-color), #ff6b6b 30%)
  );
}

.speaker-details-2 .speaker-profile-card .profile-content {
  padding: 2rem;
}

.speaker-details-2 .speaker-profile-card .profile-content .speaker-name {
  font-size: 1.8rem;
  margin-bottom: 0.5rem;
  color: var(--heading-color);
}

.speaker-details-2 .speaker-profile-card .profile-content .speaker-title {
  color: var(--accent-color);
  font-size: 1.1rem;
  font-weight: 500;
  margin-bottom: 0.75rem;
}

.speaker-details-2 .speaker-profile-card .profile-content .speaker-company {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: color-mix(in srgb, var(--default-color), transparent 30%);
  margin-bottom: 2rem;
  font-size: 1rem;
}

.speaker-details-2 .speaker-profile-card .profile-content .speaker-company i {
  color: var(--accent-color);
}

.speaker-details-2 .speaker-profile-card .profile-content .connection-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: color-mix(in srgb, var(--accent-color), transparent 95%);
  border-radius: 12px;
}

.speaker-details-2
  .speaker-profile-card
  .profile-content
  .connection-stats
  .stat-item {
  text-align: center;
}

.speaker-details-2
  .speaker-profile-card
  .profile-content
  .connection-stats
  .stat-item
  .stat-number {
  display: block;
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--accent-color);
  line-height: 1.2;
}

.speaker-details-2
  .speaker-profile-card
  .profile-content
  .connection-stats
  .stat-item
  .stat-label {
  font-size: 0.8rem;
  color: color-mix(in srgb, var(--default-color), transparent 40%);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.speaker-details-2 .speaker-profile-card .profile-content .social-connections {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.speaker-details-2
  .speaker-profile-card
  .profile-content
  .social-connections
  .social-btn {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  background: transparent;
  border: 2px solid color-mix(in srgb, var(--default-color), transparent 85%);
  border-radius: 10px;
  color: var(--default-color);
  text-decoration: none;
  transition: all 0.3s ease;
  font-weight: 500;
}

.speaker-details-2
  .speaker-profile-card
  .profile-content
  .social-connections
  .social-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px
    color-mix(in srgb, var(--default-color), transparent 85%);
}

.speaker-details-2
  .speaker-profile-card
  .profile-content
  .social-connections
  .social-btn.linkedin:hover {
  border-color: #0077b5;
  color: #0077b5;
  background: color-mix(in srgb, #0077b5, transparent 95%);
}

.speaker-details-2
  .speaker-profile-card
  .profile-content
  .social-connections
  .social-btn.twitter:hover {
  border-color: #1da1f2;
  color: #1da1f2;
  background: color-mix(in srgb, #1da1f2, transparent 95%);
}

.speaker-details-2
  .speaker-profile-card
  .profile-content
  .social-connections
  .social-btn.website:hover {
  border-color: var(--accent-color);
  color: var(--accent-color);
  background: color-mix(in srgb, var(--accent-color), transparent 95%);
}

.speaker-details-2 .speaker-highlight {
  background: linear-gradient(
    135deg,
    var(--surface-color),
    color-mix(in srgb, var(--accent-color), transparent 97%)
  );
  border-radius: 15px;
  padding: 2.5rem;
  margin-bottom: 2.5rem;
  position: relative;
  border: 1px solid color-mix(in srgb, var(--accent-color), transparent 85%);
}

.speaker-details-2 .speaker-highlight .quote-container {
  position: relative;
}

.speaker-details-2 .speaker-highlight .quote-container .quote-icon {
  position: absolute;
  top: -15px;
  left: -10px;
  width: 50px;
  height: 50px;
  background: var(--accent-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.speaker-details-2 .speaker-highlight .quote-container .quote-icon i {
  font-size: 1.5rem;
  color: var(--contrast-color);
}

.speaker-details-2 .speaker-highlight .quote-container blockquote {
  font-size: 1.25rem;
  font-style: italic;
  line-height: 1.6;
  color: var(--heading-color);
  margin: 0;
  padding-left: 3rem;
}

@media (max-width: 768px) {
  .speaker-details-2 .speaker-highlight .quote-container blockquote {
    font-size: 1.1rem;
    padding-left: 2rem;
  }
}

.speaker-details-2 .speaker-biography,
.speaker-details-2 .session-schedule {
  margin-bottom: 2.5rem;
}

.speaker-details-2 .speaker-biography .section-header,
.speaker-details-2 .session-schedule .section-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 2rem;
}

.speaker-details-2 .speaker-biography .section-header h3,
.speaker-details-2 .session-schedule .section-header h3 {
  font-size: 1.6rem;
  color: var(--heading-color);
  margin: 0;
  white-space: nowrap;
}

.speaker-details-2 .speaker-biography .section-header .header-line,
.speaker-details-2 .session-schedule .section-header .header-line {
  flex: 1;
  height: 2px;
  background: linear-gradient(90deg, var(--accent-color), transparent);
}

.speaker-details-2 .speaker-biography .bio-content p,
.speaker-details-2 .session-schedule .bio-content p {
  font-size: 1.05rem;
  line-height: 1.7;
  margin-bottom: 1.5rem;
  color: color-mix(in srgb, var(--default-color), transparent 10%);
}

.speaker-details-2 .speaker-biography .bio-content .expertise-areas,
.speaker-details-2 .session-schedule .bio-content .expertise-areas {
  margin-top: 2rem;
}

.speaker-details-2 .speaker-biography .bio-content .expertise-areas h4,
.speaker-details-2 .session-schedule .bio-content .expertise-areas h4 {
  font-size: 1.2rem;
  margin-bottom: 1rem;
  color: var(--heading-color);
}

.speaker-details-2 .speaker-biography .bio-content .expertise-areas .tags-list,
.speaker-details-2 .session-schedule .bio-content .expertise-areas .tags-list {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
}

.speaker-details-2
  .speaker-biography
  .bio-content
  .expertise-areas
  .tags-list
  .expertise-tag,
.speaker-details-2
  .session-schedule
  .bio-content
  .expertise-areas
  .tags-list
  .expertise-tag {
  background: var(--accent-color);
  color: var(--contrast-color);
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 500;
}

.speaker-details-2 .schedule-timeline {
  position: relative;
  padding-left: 2rem;
}

.speaker-details-2 .schedule-timeline::before {
  content: "";
  position: absolute;
  left: 15px;
  top: 0;
  bottom: 0;
  width: 2px;
  background: color-mix(in srgb, var(--accent-color), transparent 70%);
}

.speaker-details-2 .schedule-timeline .timeline-item {
  position: relative;
  margin-bottom: 2.5rem;
}

.speaker-details-2 .schedule-timeline .timeline-item:last-child {
  margin-bottom: 0;
}

.speaker-details-2
  .schedule-timeline
  .timeline-item.featured
  .timeline-content {
  background: color-mix(in srgb, var(--accent-color), transparent 95%);
  border-left: 4px solid var(--accent-color);
}

.speaker-details-2 .schedule-timeline .timeline-item .timeline-marker {
  position: absolute;
  left: -2rem;
  top: 0.5rem;
}

.speaker-details-2
  .schedule-timeline
  .timeline-item
  .timeline-marker
  .marker-dot {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  margin-left: 8px;
  border: 3px solid var(--surface-color);
}

.speaker-details-2
  .schedule-timeline
  .timeline-item
  .timeline-marker
  .marker-dot.keynote {
  background: var(--accent-color);
}

.speaker-details-2
  .schedule-timeline
  .timeline-item
  .timeline-marker
  .marker-dot.workshop {
  background: #10b981;
}

.speaker-details-2
  .schedule-timeline
  .timeline-item
  .timeline-marker
  .marker-dot.panel {
  background: #8b5cf6;
}

.speaker-details-2 .schedule-timeline .timeline-item .timeline-content {
  background: var(--surface-color);
  padding: 2rem;
  border-radius: 12px;
  border: 1px solid color-mix(in srgb, var(--default-color), transparent 90%);
}

.speaker-details-2
  .schedule-timeline
  .timeline-item
  .timeline-content
  .session-tag {
  display: inline-block;
  padding: 0.3rem 0.8rem;
  border-radius: 15px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  margin-bottom: 1rem;
}

.speaker-details-2
  .schedule-timeline
  .timeline-item
  .timeline-content
  .session-tag.keynote {
  background: var(--accent-color);
  color: var(--contrast-color);
}

.speaker-details-2
  .schedule-timeline
  .timeline-item
  .timeline-content
  .session-tag.workshop {
  background: #10b981;
  color: white;
}

.speaker-details-2
  .schedule-timeline
  .timeline-item
  .timeline-content
  .session-tag.panel {
  background: #8b5cf6;
  color: white;
}

.speaker-details-2
  .schedule-timeline
  .timeline-item
  .timeline-content
  .session-name {
  font-size: 1.3rem;
  margin-bottom: 1.5rem;
  color: var(--heading-color);
}

.speaker-details-2
  .schedule-timeline
  .timeline-item
  .timeline-content
  .session-meta-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 1rem;
}

@media (max-width: 768px) {
  .speaker-details-2
    .schedule-timeline
    .timeline-item
    .timeline-content
    .session-meta-grid {
    grid-template-columns: 1fr;
  }
}

.speaker-details-2
  .schedule-timeline
  .timeline-item
  .timeline-content
  .session-meta-grid
  .meta-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: color-mix(in srgb, var(--default-color), transparent 30%);
  font-size: 0.95rem;
}

.speaker-details-2
  .schedule-timeline
  .timeline-item
  .timeline-content
  .session-meta-grid
  .meta-item
  i {
  color: var(--accent-color);
  font-size: 1rem;
}

.speaker-details-2
  .schedule-timeline
  .timeline-item
  .timeline-content
  .session-summary {
  line-height: 1.6;
  margin: 0;
  color: color-mix(in srgb, var(--default-color), transparent 20%);
}

.speaker-details-2 .speaker-cta {
  padding: 2rem 0;
  border-top: 1px solid
    color-mix(in srgb, var(--default-color), transparent 90%);
}

.speaker-details-2 .speaker-cta .cta-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  justify-content: center;
}

@media (max-width: 768px) {
  .speaker-details-2 .speaker-cta .cta-buttons {
    flex-direction: column;
    align-items: stretch;
  }
}

.speaker-details-2 .speaker-cta .cta-buttons a {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.875rem 2rem;
  border-radius: 25px;
  font-weight: 600;
  text-decoration: none;
  transition: all 0.3s ease;
  justify-content: center;
}

.speaker-details-2 .speaker-cta .cta-buttons a.btn-primary {
  background: var(--accent-color);
  color: var(--contrast-color);
  border: 2px solid var(--accent-color);
}

.speaker-details-2 .speaker-cta .cta-buttons a.btn-primary:hover {
  background: transparent;
  color: var(--accent-color);
  transform: translateY(-2px);
}

.speaker-details-2 .speaker-cta .cta-buttons a.btn-secondary {
  background: var(--heading-color);
  color: var(--background-color);
  border: 2px solid var(--heading-color);
}

.speaker-details-2 .speaker-cta .cta-buttons a.btn-secondary:hover {
  background: transparent;
  color: var(--heading-color);
  transform: translateY(-2px);
}

.speaker-details-2 .speaker-cta .cta-buttons a.btn-outline {
  background: transparent;
  color: var(--default-color);
  border: 2px solid color-mix(in srgb, var(--default-color), transparent 70%);
}

.speaker-details-2 .speaker-cta .cta-buttons a.btn-outline:hover {
  background: var(--default-color);
  color: var(--background-color);
  border-color: var(--default-color);
  transform: translateY(-2px);
}

/*--------------------------------------------------------------
# Tickets Section
--------------------------------------------------------------*/
.tickets .ticket-card {
  background: var(--surface-color);
  border-radius: 16px;
  padding: 40px 30px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  position: relative;
  border: 2px solid transparent;
  height: 100%;
}

.tickets .ticket-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
}

.tickets .ticket-card.featured {
  border-color: var(--accent-color);
  transform: scale(1.05);
  background: linear-gradient(
    135deg,
    var(--surface-color) 0%,
    color-mix(in srgb, var(--accent-color), transparent 95%) 100%
  );
}

.tickets .ticket-card.featured:hover {
  transform: scale(1.05) translateY(-5px);
}

.tickets .popular-badge {
  position: absolute;
  top: -12px;
  left: 50%;
  transform: translateX(-50%);
  background: var(--accent-color);
  color: var(--contrast-color);
  padding: 8px 20px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.tickets .ticket-header {
  text-align: center;
  margin-bottom: 30px;
}

.tickets .ticket-header h3 {
  font-size: 28px;
  font-weight: 700;
  margin-bottom: 15px;
  color: var(--heading-color);
}

.tickets .ticket-header .ticket-price {
  margin-bottom: 10px;
}

.tickets .ticket-header .ticket-price .currency {
  font-size: 20px;
  color: var(--accent-color);
  font-weight: 600;
  vertical-align: top;
}

.tickets .ticket-header .ticket-price .amount {
  font-size: 48px;
  font-weight: 700;
  color: var(--accent-color);
  line-height: 1;
}

.tickets .ticket-header .ticket-price .period {
  font-size: 16px;
  color: color-mix(in srgb, var(--default-color), transparent 40%);
  font-weight: 500;
}

.tickets .ticket-header .ticket-price .original-price {
  font-size: 18px;
  color: color-mix(in srgb, var(--default-color), transparent 50%);
  text-decoration: line-through;
  margin-right: 10px;
}

.tickets .ticket-header .ticket-duration {
  color: color-mix(in srgb, var(--default-color), transparent 30%);
  font-size: 16px;
  font-weight: 500;
  margin: 0;
}

.tickets .ticket-body {
  margin-bottom: 30px;
}

.tickets .ticket-body .ticket-features {
  list-style: none;
  padding: 0;
  margin: 0;
}

.tickets .ticket-body .ticket-features li {
  display: flex;
  align-items: flex-start;
  margin-bottom: 15px;
  font-size: 15px;
  line-height: 1.5;
  color: var(--default-color);
}

.tickets .ticket-body .ticket-features li i {
  color: var(--accent-color);
  font-size: 18px;
  margin-right: 12px;
  margin-top: 2px;
  flex-shrink: 0;
}

.tickets .ticket-body .ticket-features li:last-child {
  margin-bottom: 0;
}

.tickets .ticket-footer {
  text-align: center;
}

.tickets .ticket-footer .btn-ticket {
  background: var(--accent-color);
  color: var(--contrast-color);
  padding: 15px 30px;
  border-radius: 50px;
  font-weight: 600;
  font-size: 16px;
  text-decoration: none;
  display: inline-block;
  transition: all 0.3s ease;
  border: 2px solid var(--accent-color);
  width: 100%;
  margin-bottom: 15px;
}

.tickets .ticket-footer .btn-ticket:hover {
  background: var(--contrast-color);
  color: var(--accent-color);
  transform: translateY(-2px);
}

.tickets .ticket-footer .availability-info {
  margin: 0;
  font-size: 14px;
  color: color-mix(in srgb, var(--default-color), transparent 40%);
  font-weight: 500;
}

.tickets .ticket-info-bar {
  background: color-mix(in srgb, var(--accent-color), transparent 95%);
  border-radius: 16px;
  padding: 40px;
  text-align: center;
  border: 1px solid color-mix(in srgb, var(--accent-color), transparent 85%);
}

.tickets .ticket-info-bar .countdown-info {
  margin-bottom: 30px;
}

.tickets .ticket-info-bar .countdown-info h4 {
  color: var(--heading-color);
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 20px;
}

.tickets .ticket-info-bar .countdown-info h4 i {
  color: var(--accent-color);
  margin-right: 10px;
}

.tickets .ticket-info-bar .countdown-info .countdown {
  display: flex;
  justify-content: center;
  gap: 30px;
}

.tickets .ticket-info-bar .countdown-info .countdown > div {
  background: var(--surface-color);
  padding: 20px 15px;
  border-radius: 10px;
  min-width: 80px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.tickets .ticket-info-bar .countdown-info .countdown > div h3 {
  font-size: 32px;
  font-weight: 700;
  color: var(--accent-color);
  margin: 0 0 5px 0;
  line-height: 1;
}

.tickets .ticket-info-bar .countdown-info .countdown > div h4 {
  font-size: 14px;
  font-weight: 500;
  color: color-mix(in srgb, var(--default-color), transparent 30%);
  margin: 0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.tickets .ticket-info-bar .support-info p {
  margin-bottom: 10px;
  font-size: 16px;
  color: var(--default-color);
}

.tickets .ticket-info-bar .support-info .contact-link {
  color: var(--accent-color);
  text-decoration: none;
  font-weight: 600;
}

.tickets .ticket-info-bar .support-info .contact-link:hover {
  color: color-mix(in srgb, var(--accent-color), transparent 25%);
}

.tickets .ticket-info-bar .support-info .divider {
  margin: 0 15px;
  color: color-mix(in srgb, var(--default-color), transparent 50%);
}

@media (max-width: 768px) {
  .tickets .ticket-card {
    padding: 30px 20px;
  }

  .tickets .ticket-card.featured {
    transform: none;
  }

  .tickets .ticket-card.featured:hover {
    transform: translateY(-5px);
  }

  .tickets .ticket-header .ticket-price .amount {
    font-size: 36px;
  }

  .tickets .ticket-info-bar {
    padding: 30px 20px;
  }

  .tickets .ticket-info-bar .countdown {
    gap: 15px !important;
  }

  .tickets .ticket-info-bar .countdown > div {
    padding: 15px 10px;
    min-width: 60px;
  }

  .tickets .ticket-info-bar .countdown > div h3 {
    font-size: 24px;
  }

  .tickets .ticket-info-bar .countdown > div h4 {
    font-size: 12px;
  }

  .tickets .ticket-info-bar .support-info .divider {
    display: block;
    margin: 10px 0;
  }
}

/*--------------------------------------------------------------
# Buy Tickets Section
--------------------------------------------------------------*/
.buy-tickets {
  background-color: color-mix(
    in srgb,
    var(--background-color),
    var(--surface-color) 5%
  );
}

.buy-tickets .ticket-form-wrapper {
  background-color: var(--surface-color);
  border-radius: 12px;
  padding: 40px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  border: 1px solid color-mix(in srgb, var(--default-color), transparent 90%);
}

.buy-tickets .event-info {
  text-align: center;
  border-bottom: 2px solid
    color-mix(in srgb, var(--accent-color), transparent 80%);
  padding-bottom: 30px;
}

.buy-tickets .event-info h3 {
  color: var(--heading-color);
  margin-bottom: 20px;
  font-size: 2rem;
  font-weight: 700;
}

.buy-tickets .event-info .event-meta {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  gap: 30px;
}

.buy-tickets .event-info .event-meta span {
  display: flex;
  align-items: center;
  color: var(--default-color);
  font-size: 14px;
}

.buy-tickets .event-info .event-meta span i {
  color: var(--accent-color);
  margin-right: 8px;
  font-size: 16px;
}

@media (max-width: 768px) {
  .buy-tickets .event-info .event-meta {
    flex-direction: column;
    gap: 15px;
  }
}

.buy-tickets .ticket-types {
  margin-bottom: 40px;
}

.buy-tickets .ticket-types h4 {
  color: var(--heading-color);
  margin-bottom: 25px;
  font-size: 1.5rem;
  font-weight: 600;
}

.buy-tickets .ticket-types .ticket-option {
  margin-bottom: 20px;
}

.buy-tickets .ticket-types .ticket-option input[type="radio"] {
  display: none;
}

.buy-tickets .ticket-types .ticket-option .ticket-label {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 25px;
  border: 2px solid color-mix(in srgb, var(--default-color), transparent 85%);
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  background-color: var(--surface-color);
}

.buy-tickets .ticket-types .ticket-option .ticket-label:hover {
  border-color: color-mix(in srgb, var(--accent-color), transparent 50%);
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.buy-tickets
  .ticket-types
  .ticket-option
  input[type="radio"]:checked
  + .ticket-label {
  border-color: var(--accent-color);
  background-color: color-mix(in srgb, var(--accent-color), transparent 95%);
  box-shadow: 0 5px 20px
    color-mix(in srgb, var(--accent-color), transparent 80%);
}

.buy-tickets .ticket-types .ticket-option .ticket-info {
  flex: 1;
}

.buy-tickets .ticket-types .ticket-option .ticket-info .ticket-name {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--heading-color);
  margin-bottom: 8px;
}

.buy-tickets .ticket-types .ticket-option .ticket-info .ticket-description {
  color: var(--default-color);
  font-size: 14px;
  margin-bottom: 12px;
}

.buy-tickets .ticket-types .ticket-option .ticket-info .ticket-benefits {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.buy-tickets .ticket-types .ticket-option .ticket-info .ticket-benefits span {
  font-size: 12px;
  color: color-mix(in srgb, var(--default-color), transparent 20%);
  background-color: color-mix(in srgb, var(--accent-color), transparent 90%);
  padding: 4px 8px;
  border-radius: 4px;
}

.buy-tickets .ticket-types .ticket-option .ticket-price {
  text-align: right;
}

.buy-tickets .ticket-types .ticket-option .ticket-price .original-price {
  color: color-mix(in srgb, var(--default-color), transparent 50%);
  text-decoration: line-through;
  font-size: 14px;
  display: block;
  margin-bottom: 4px;
}

.buy-tickets .ticket-types .ticket-option .ticket-price .current-price {
  color: var(--accent-color);
  font-size: 1.5rem;
  font-weight: 700;
}

@media (max-width: 768px) {
  .buy-tickets .ticket-types .ticket-option .ticket-label {
    flex-direction: column;
    text-align: center;
    gap: 15px;
  }

  .buy-tickets .ticket-types .ticket-option .ticket-benefits {
    justify-content: center;
  }
}

.buy-tickets .ticket-form .form-group {
  margin-bottom: 25px;
}

.buy-tickets .ticket-form .form-group label {
  color: var(--heading-color);
  font-weight: 500;
  margin-bottom: 8px;
  display: block;
  font-size: 14px;
}

.buy-tickets .ticket-form .form-group input[type="text"],
.buy-tickets .ticket-form .form-group input[type="email"],
.buy-tickets .ticket-form .form-group input[type="tel"],
.buy-tickets .ticket-form .form-group select,
.buy-tickets .ticket-form .form-group textarea {
  color: var(--default-color);
  background-color: var(--surface-color);
  font-size: 14px;
  border: 2px solid color-mix(in srgb, var(--default-color), transparent 85%);
  border-radius: 8px;
  padding: 12px 16px;
  width: 100%;
  transition: all 0.3s ease;
}

.buy-tickets .ticket-form .form-group input[type="text"]:focus,
.buy-tickets .ticket-form .form-group input[type="email"]:focus,
.buy-tickets .ticket-form .form-group input[type="tel"]:focus,
.buy-tickets .ticket-form .form-group select:focus,
.buy-tickets .ticket-form .form-group textarea:focus {
  border-color: var(--accent-color);
  outline: none;
  box-shadow: 0 0 0 3px color-mix(in srgb, var(--accent-color), transparent 90%);
}

.buy-tickets .ticket-form .form-group input[type="text"]::placeholder,
.buy-tickets .ticket-form .form-group input[type="email"]::placeholder,
.buy-tickets .ticket-form .form-group input[type="tel"]::placeholder,
.buy-tickets .ticket-form .form-group select::placeholder,
.buy-tickets .ticket-form .form-group textarea::placeholder {
  color: color-mix(in srgb, var(--default-color), transparent 70%);
}

.buy-tickets .ticket-form .form-group select {
  appearance: none;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 12px center;
  background-repeat: no-repeat;
  background-size: 16px;
  padding-right: 40px;
}

.buy-tickets .pricing-summary {
  background-color: color-mix(in srgb, var(--accent-color), transparent 95%);
  border: 1px solid color-mix(in srgb, var(--accent-color), transparent 80%);
  border-radius: 10px;
  padding: 25px;
  margin: 30px 0;
}

.buy-tickets .pricing-summary h5 {
  color: var(--heading-color);
  margin-bottom: 20px;
  font-size: 1.2rem;
  font-weight: 600;
}

.buy-tickets .pricing-summary .summary-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12px;
  color: var(--default-color);
}

.buy-tickets .pricing-summary .summary-row.total {
  margin-top: 15px;
  padding-top: 15px;
  border-top: 2px solid color-mix(in srgb, var(--accent-color), transparent 70%);
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--heading-color);
}

.buy-tickets .pricing-summary .summary-row.total .total-price {
  color: var(--accent-color);
  font-size: 1.3rem;
  font-weight: 700;
}

.buy-tickets .pricing-summary .tax-note {
  font-size: 12px;
  color: color-mix(in srgb, var(--default-color), transparent 40%);
  text-align: center;
  margin-top: 15px;
  font-style: italic;
}

.buy-tickets .terms-checkbox,
.buy-tickets .newsletter-checkbox {
  margin-bottom: 20px;
}

.buy-tickets .terms-checkbox input[type="checkbox"],
.buy-tickets .newsletter-checkbox input[type="checkbox"] {
  margin-right: 10px;
}

.buy-tickets .terms-checkbox label,
.buy-tickets .newsletter-checkbox label {
  color: var(--default-color);
  font-size: 14px;
  cursor: pointer;
}

.buy-tickets .terms-checkbox label a,
.buy-tickets .newsletter-checkbox label a {
  color: var(--accent-color);
  text-decoration: none;
}

.buy-tickets .terms-checkbox label a:hover,
.buy-tickets .newsletter-checkbox label a:hover {
  text-decoration: underline;
}

.buy-tickets .form-submit {
  text-align: center;
  margin-top: 30px;
}

.buy-tickets .form-submit .btn-submit {
  background: linear-gradient(
    135deg,
    var(--accent-color),
    color-mix(in srgb, var(--accent-color), #000 15%)
  );
  color: var(--contrast-color);
  border: none;
  padding: 16px 40px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 250px;
}

.buy-tickets .form-submit .btn-submit:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px
    color-mix(in srgb, var(--accent-color), transparent 70%);
}

.buy-tickets .form-submit .btn-submit:active {
  transform: translateY(0);
}

.buy-tickets .security-info {
  margin-top: 40px;
  text-align: center;
  border-top: 1px solid
    color-mix(in srgb, var(--default-color), transparent 90%);
  padding-top: 30px;
}

.buy-tickets .security-info .security-badges {
  display: flex;
  justify-content: center;
  gap: 30px;
  margin-bottom: 25px;
}

.buy-tickets .security-info .security-badges .badge-item {
  display: flex;
  align-items: center;
  gap: 8px;
  color: color-mix(in srgb, var(--default-color), transparent 20%);
  font-size: 14px;
}

.buy-tickets .security-info .security-badges .badge-item i {
  color: var(--accent-color);
  font-size: 18px;
}

.buy-tickets .security-info .payment-methods {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15px;
  flex-wrap: wrap;
}

.buy-tickets .security-info .payment-methods .payment-label {
  color: var(--default-color);
  font-size: 14px;
  font-weight: 500;
}

.buy-tickets .security-info .payment-methods .payment-icons {
  display: flex;
  align-items: center;
  gap: 10px;
}

.buy-tickets .security-info .payment-methods .payment-icons i {
  color: var(--accent-color);
  font-size: 24px;
}

.buy-tickets .security-info .payment-methods .payment-icons .payment-text {
  color: color-mix(in srgb, var(--default-color), transparent 30%);
  font-size: 13px;
}

@media (max-width: 768px) {
  .buy-tickets .security-info .security-badges {
    flex-direction: column;
    gap: 15px;
  }

  .buy-tickets .security-info .payment-methods {
    flex-direction: column;
    gap: 10px;
  }
}

@media (max-width: 768px) {
  .buy-tickets .ticket-form-wrapper {
    padding: 25px 20px;
  }
}

/*--------------------------------------------------------------
# Venue 2 Section
--------------------------------------------------------------*/
.venue-2 .venue-content h3 {
  color: var(--heading-color);
  margin-bottom: 20px;
  font-size: 2rem;
}

.venue-2 .venue-content .venue-address {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 20px;
  color: var(--accent-color);
  font-weight: 500;
}

.venue-2 .venue-content .venue-address i {
  font-size: 1.2rem;
}

.venue-2 .venue-content p {
  margin-bottom: 30px;
  line-height: 1.7;
}

.venue-2 .venue-image {
  position: relative;
}

.venue-2 .venue-image img {
  border-radius: 15px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.venue-2 .venue-image .venue-badge {
  position: absolute;
  top: 20px;
  left: 20px;
  background: var(--accent-color);
  color: var(--contrast-color);
  padding: 8px 16px;
  border-radius: 25px;
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 0.9rem;
  font-weight: 500;
}

.venue-2 .venue-features {
  margin-bottom: 30px;
}

.venue-2 .venue-features .feature-item {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 15px;
}

.venue-2 .venue-features .feature-item i {
  color: var(--accent-color);
  font-size: 1.1rem;
  width: 20px;
}

.venue-2 .venue-features .feature-item span {
  font-weight: 500;
}

.venue-2 .venue-actions {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
}

.venue-2 .venue-actions .btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  border-radius: 25px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.venue-2 .venue-actions .btn.btn-primary {
  background-color: var(--accent-color);
  border-color: var(--accent-color);
  color: var(--contrast-color);
}

.venue-2 .venue-actions .btn.btn-primary:hover {
  background-color: color-mix(in srgb, var(--accent-color), black 10%);
  border-color: color-mix(in srgb, var(--accent-color), black 10%);
  transform: translateY(-2px);
}

.venue-2 .venue-actions .btn.btn-outline-primary {
  border-color: var(--accent-color);
  color: var(--accent-color);
}

.venue-2 .venue-actions .btn.btn-outline-primary:hover {
  background-color: var(--accent-color);
  color: var(--contrast-color);
  transform: translateY(-2px);
}

.venue-2 .venue-map h4 {
  color: var(--heading-color);
  margin-bottom: 25px;
  text-align: center;
}

.venue-2 .venue-map .map-container {
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.venue-2 .venue-map .map-container iframe {
  width: 100%;
  height: 400px;
}

.venue-2 .travel-info {
  text-align: center;
  padding: 30px 20px;
  background-color: var(--surface-color);
  border-radius: 15px;
  height: 100%;
  transition: transform 0.3s ease;
}

.venue-2 .travel-info:hover {
  transform: translateY(-5px);
}

.venue-2 .travel-info .travel-icon {
  width: 70px;
  height: 70px;
  margin: 0 auto 20px;
  background-color: color-mix(in srgb, var(--accent-color), transparent 10%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.venue-2 .travel-info .travel-icon i {
  font-size: 1.8rem;
  color: var(--contrast-color);
}

.venue-2 .travel-info h5 {
  color: var(--heading-color);
  margin-bottom: 15px;
}

.venue-2 .travel-info p {
  margin: 0;
  line-height: 1.6;
}

.venue-2 .accommodation-section h4 {
  color: var(--heading-color);
  margin-bottom: 30px;
  text-align: center;
}

.venue-2 .accommodation-section .hotel-card {
  background-color: var(--surface-color);
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
  height: 100%;
}

.venue-2 .accommodation-section .hotel-card:hover {
  transform: translateY(-5px);
}

.venue-2 .accommodation-section .hotel-card img {
  width: 100%;
  height: 200px;
  object-fit: cover;
}

.venue-2 .accommodation-section .hotel-card .hotel-info {
  padding: 20px;
}

.venue-2 .accommodation-section .hotel-card .hotel-info h6 {
  color: var(--heading-color);
  margin-bottom: 10px;
  font-weight: 600;
}

.venue-2 .accommodation-section .hotel-card .hotel-info .hotel-distance {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-bottom: 8px;
  font-size: 0.9rem;
}

.venue-2 .accommodation-section .hotel-card .hotel-info .hotel-distance i {
  color: var(--accent-color);
  font-size: 0.8rem;
}

.venue-2 .accommodation-section .hotel-card .hotel-info .hotel-price {
  font-weight: 600;
  color: var(--accent-color);
}

@media (max-width: 768px) {
  .venue-2 .venue-actions {
    justify-content: center;
  }

  .venue-2 .venue-actions .btn {
    flex: 1;
    min-width: 150px;
  }

  .venue-2 .venue-map .map-container iframe {
    height: 300px;
  }

  .venue-2 .travel-info {
    margin-bottom: 30px;
  }

  .venue-2 .hotel-card {
    margin-bottom: 30px;
  }
}

/*--------------------------------------------------------------
# Sponsors Section
--------------------------------------------------------------*/
.sponsors .clients-wrap {
  border-top: 1px solid
    color-mix(in srgb, var(--default-color), transparent 85%);
  border-left: 1px solid
    color-mix(in srgb, var(--default-color), transparent 85%);
}

.sponsors .client-logo {
  background-color: var(--surface-color);
  display: flex;
  justify-content: center;
  align-items: center;
  border-right: 1px solid
    color-mix(in srgb, var(--default-color), transparent 85%);
  border-bottom: 1px solid
    color-mix(in srgb, var(--default-color), transparent 85%);
  overflow: hidden;
}

.sponsors .client-logo img {
  padding: 50px;
  max-width: 80%;
  transition: 0.3s;
}

@media (max-width: 640px) {
  .sponsors .client-logo img {
    padding: 30px;
    max-width: 50%;
  }
}

.sponsors .client-logo:hover img {
  transform: scale(1.1);
}

/*--------------------------------------------------------------
# Contact Section
--------------------------------------------------------------*/
.contact .contact-info-box {
  background-color: var(--surface-color);
  border-radius: 10px;
  box-shadow: 0 5px 25px rgba(0, 0, 0, 0.05);
  padding: 25px;
  height: 100%;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  display: flex;
  align-items: flex-start;
  gap: 15px;
}

.contact .contact-info-box:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
}

.contact .contact-info-box .icon-box {
  background-color: color-mix(in srgb, var(--accent-color), transparent 90%);
  color: var(--accent-color);
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.contact .contact-info-box .icon-box i {
  font-size: 24px;
}

.contact .contact-info-box .info-content h4 {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 10px;
}

.contact .contact-info-box .info-content p {
  margin-bottom: 5px;
  color: color-mix(in srgb, var(--default-color), transparent 20%);
  font-size: 15px;
  line-height: 1.5;
}

.contact .contact-info-box .info-content p:last-child {
  margin-bottom: 0;
}

.contact .map-section {
  position: relative;
  width: 100%;
  height: 500px;
  overflow: hidden;
}

.contact .map-section iframe {
  display: block;
  width: 100%;
  height: 100%;
  border: 0;
}

.contact .form-container-overlap {
  position: relative;
  margin-top: -150px;
  margin-bottom: 60px;
  z-index: 10;
}

.contact .contact-form-wrapper {
  background-color: var(--surface-color);
  border-radius: 12px;
  box-shadow: 0 5px 25px rgba(0, 0, 0, 0.05);
  padding: 40px;
}

.contact .contact-form-wrapper h2 {
  font-size: 28px;
  font-weight: 700;
  margin-bottom: 30px;
  position: relative;
}

.contact .contact-form-wrapper h2:after {
  content: "";
  position: absolute;
  left: 50%;
  bottom: -10px;
  transform: translateX(-50%);
  width: 50px;
  height: 3px;
  background-color: var(--accent-color);
}

.contact .contact-form-wrapper .form-group {
  margin-bottom: 20px;
}

.contact .contact-form-wrapper .form-group .input-with-icon {
  position: relative;
}

.contact .contact-form-wrapper .form-group .input-with-icon i {
  position: absolute;
  left: 15px;
  top: 50%;
  transform: translateY(-50%);
  color: color-mix(in srgb, var(--default-color), transparent 40%);
  font-size: 18px;
  z-index: 10;
}

.contact .contact-form-wrapper .form-group .input-with-icon i.message-icon {
  top: 28px;
}

.contact .contact-form-wrapper .form-group .input-with-icon textarea + i {
  top: 25px;
  transform: none;
}

.contact .contact-form-wrapper .form-group .input-with-icon .form-control {
  border-radius: 8px;
  padding: 12px 15px 12px 45px;
  height: 3.5rem;
  color: var(--default-color);
  background-color: var(--surface-color);
  font-size: 15px;
  border: 1px solid color-mix(in srgb, var(--default-color), transparent 80%);
}

.contact
  .contact-form-wrapper
  .form-group
  .input-with-icon
  .form-control:focus {
  border-color: var(--accent-color);
  box-shadow: 0 0 0 0.25rem
    color-mix(in srgb, var(--accent-color), transparent 90%);
}

.contact
  .contact-form-wrapper
  .form-group
  .input-with-icon
  .form-control::placeholder {
  color: color-mix(in srgb, var(--default-color), transparent 40%);
}

.contact
  .contact-form-wrapper
  .form-group
  .input-with-icon
  textarea.form-control {
  height: 180px;
  resize: none;
  padding-top: 15px;
}

.contact .contact-form-wrapper .btn-submit {
  background-color: var(--accent-color);
  border: none;
  color: var(--contrast-color);
  padding: 12px 30px;
  font-size: 16px;
  font-weight: 600;
  letter-spacing: 1px;
  border-radius: 8px;
  transition: all 0.3s ease;
  box-shadow: 0 5px 15px
    color-mix(in srgb, var(--accent-color), transparent 70%);
}

.contact .contact-form-wrapper .btn-submit:hover {
  background-color: color-mix(in srgb, var(--accent-color), transparent 15%);
  transform: translateY(-3px);
  box-shadow: 0 8px 20px
    color-mix(in srgb, var(--accent-color), transparent 60%);
}

.contact .contact-form-wrapper .btn-submit:active {
  transform: translateY(0);
  box-shadow: 0 3px 10px
    color-mix(in srgb, var(--accent-color), transparent 70%);
}

.contact .contact-form-wrapper .loading,
.contact .contact-form-wrapper .error-message,
.contact .contact-form-wrapper .sent-message {
  margin-top: 10px;
  margin-bottom: 20px;
}

@media (max-width: 992px) {
  .contact .form-container-overlap {
    margin-top: -120px;
  }

  .contact .contact-form-wrapper {
    padding: 30px;
  }
}

@media (max-width: 768px) {
  .contact .contact-info-box {
    margin-bottom: 20px;
  }

  .contact .form-container-overlap {
    margin-top: -100px;
  }

  .contact .contact-form-wrapper {
    padding: 25px;
  }

  .contact .contact-form-wrapper h2 {
    font-size: 24px;
  }

  .contact .map-section {
    height: 450px;
  }
}

@media (max-width: 576px) {
  .contact .form-container-overlap {
    margin-top: -80px;
  }

  .contact .contact-form-wrapper {
    padding: 20px;
  }

  .contact .btn-submit {
    width: 100%;
  }

  .contact .map-section {
    height: 400px;
  }
}

/*--------------------------------------------------------------
# Terms Of Service Section
--------------------------------------------------------------*/
/* Service Details Enhancements */
.service-hero-image {
  overflow: hidden;
}

.service-hero-image img {
  transition: transform 0.3s ease;
}

.service-hero-image:hover img {
  transform: scale(1.05);
}

.feature-card {
  transition: all 0.3s ease;
  border: 1px solid transparent;
}

.feature-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  border-color: #007bff;
}

.service-info-card {
  background: linear-gradient(135deg, #007bff, #0056b3);
}

.related-services .list-group-item:hover {
  background-color: #e3f2fd !important;
  transform: translateX(5px);
  transition: all 0.3s ease;
}

/* Responsive Improvements */
@media (max-width: 768px) {
  .service-hero-image {
    margin-bottom: 2rem;
  }

  .feature-card {
    margin-bottom: 1rem;
  }

  .service-sidebar {
    margin-top: 2rem;
  }
}

.call-to-action .lead {
  font-size: 1.125rem;
  line-height: 1.6;
  font-weight: 400;
}

.call-to-action .stats-wrapper {
  background: var(--surface-color);
  border-radius: 16px;
  padding: 3rem 2rem;
  backdrop-filter: blur(10px);
  border: 1px solid color-mix(in srgb, var(--default-color), transparent 90%);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.08);
}

.call-to-action .stat-item {
  text-align: center;
  padding: 1rem 0;
}

.call-to-action .stat-item .stat-icon {
  width: 75px;
  height: 75px;
  background: color-mix(in srgb, var(--accent-color), transparent 90%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1rem;
  transition: all 0.3s ease;
}

.call-to-action .stat-item .stat-icon i {
  font-size: 2.5rem;
  color: var(--accent-color);
}

.call-to-action .stat-item .stat-number {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--heading-color);
  line-height: 1;
  margin-bottom: 0.5rem;
}

.call-to-action .stat-item .stat-label {
  font-size: 0.975rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.call-to-action .stat-item:hover .stat-icon {
  background: var(--accent-color);
  transform: translateY(-2px);
}

.call-to-action .stat-item:hover .stat-icon i {
  color: var(--contrast-color);
}

.call-to-action .cta-content h3 {
  font-size: 1.75rem;
  font-weight: 600;
  color: var(--heading-color);
  margin-bottom: 1rem;
}

.call-to-action .cta-content p {
  color: color-mix(in srgb, var(--default-color), transparent 20%);
  font-size: 1.125rem;
  line-height: 1.6;
}

.call-to-action .cta-buttons {
  margin-bottom: 2rem;
}

.call-to-action .cta-buttons .btn {
  border-radius: 50px;
  padding: 0.875rem 2rem;
  font-weight: 600;
  font-size: 1rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  transition: all 0.3s ease;
  border-width: 2px;
}

.call-to-action .cta-buttons .btn.btn-primary {
  background: var(--accent-color);
  border-color: var(--accent-color);
  color: var(--contrast-color);
}

.call-to-action .cta-buttons .btn.btn-primary:hover {
  background: color-mix(in srgb, var(--accent-color), black 15%);
  border-color: color-mix(in srgb, var(--accent-color), black 15%);
  transform: translateY(-2px);
}

/*--------------------------------------------------------------
  # Services Page Styles
  --------------------------------------------------------------*/
.services-page {
  background-color: #f8f9fa;
}

/* Service Items */
.service-item {
  background: #fff;
  border-radius: 15px;
  padding: 2rem;
  box-shadow: 0 5px 25px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  border: 1px solid #e9ecef;
  position: relative;
  overflow: hidden;
}

.service-item::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  /* background: linear-gradient(45deg, #007bff, #0056b3); */
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.service-item:hover::before {
  transform: scaleX(1);
}

.service-item:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 40px rgba(0, 123, 255, 0.15);
}

.service-icon {
  width: 80px;
  height: 80px;
  /* background: linear-gradient(45deg, #007bff, #0056b3); */
  background: var(--accent-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1.5rem;
  transition: all 0.3s ease;
}

.service-icon i {
  font-size: 2rem;
  color: white;
}

.service-item:hover .service-icon {
  transform: scale(1.1) rotate(5deg);
}

.service-content h4 {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: #2c3e50;
}

.service-content p {
  color: #6c757d;
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

.service-features {
  margin-bottom: 1.5rem;
}

.service-features .badge {
  font-size: 0.75rem;
  padding: 0.5rem 0.75rem;
  margin-bottom: 0.5rem;
}

.read-more {
  display: inline-flex;
  align-items: center;
  /* color: #007bff; */
  color: var(--accent-color);
  font-weight: 600;
  text-decoration: none;
  transition: all 0.3s ease;
}

.read-more:hover {
  color: #0056b3;
  transform: translateX(5px);
}

.read-more i {
  margin-left: 0.5rem;
  transition: transform 0.3s ease;
}

.read-more:hover i {
  transform: translateX(3px);
}

/* Why Choose Us Section */
.why-choose-us {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.feature-box {
  background: white;
  border-radius: 15px;
  transition: all 0.3s ease;
  border: 1px solid #e9ecef;
}

.feature-box:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.feature-box i {
  transition: all 0.3s ease;
}

.feature-box:hover i {
  transform: scale(1.1);
}

/* Enhanced Page Title */
.page-title {
  position: relative;
  background-attachment: fixed;
  background-size: cover;
  background-position: center;
}

/* .page-title::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(0, 123, 255, 0.8), rgba(0, 86, 179, 0.8));
  } */

.page-title .container {
  position: relative;
  z-index: 2;
}

.page-title h1 {
  font-size: 3.5rem;
  font-weight: 800;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
  margin-bottom: 1rem;
}

.page-title p {
  font-size: 1.2rem;
  opacity: 0.9;
  max-width: 600px;
  margin: 0 auto 2rem;
}

/* Section Title Enhancement */
.section-title h2 {
  font-size: 2.5rem;
  font-weight: 700;
  color: #2c3e50;
  position: relative;
  display: inline-block;
}

/* .section-title h2::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: linear-gradient(45deg, #007bff, #0056b3);
    border-radius: 2px;
  } */

/* Responsive Design */
@media (max-width: 768px) {
  .service-item {
    padding: 1.5rem;
    margin-bottom: 2rem;
  }

  .service-icon {
    width: 60px;
    height: 60px;
  }

  .service-icon i {
    font-size: 1.5rem;
  }

  .service-content h4 {
    font-size: 1.25rem;
  }

  .page-title h1 {
    font-size: 2.5rem;
  }

  .page-title p {
    font-size: 1rem;
  }

  .section-title h2 {
    font-size: 2rem;
  }
}

@media (max-width: 576px) {
  .service-item {
    padding: 1rem;
  }

  .page-title {
    padding: 120px 0 60px 0;
  }

  .page-title h1 {
    font-size: 2rem;
  }
}

/* Animation Classes */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.service-item {
  animation: fadeInUp 0.6s ease forwards;
}

.service-item:nth-child(1) {
  animation-delay: 0.1s;
}
.service-item:nth-child(2) {
  animation-delay: 0.2s;
}
.service-item:nth-child(3) {
  animation-delay: 0.3s;
}
.service-item:nth-child(4) {
  animation-delay: 0.4s;
}
.service-item:nth-child(5) {
  animation-delay: 0.5s;
}
.service-item:nth-child(6) {
  animation-delay: 0.6s;
}

/*--------------------------------------------------------------
  # Enhanced Service Details Pages
  --------------------------------------------------------------*/

/* Service Details Section */
.service-details {
  padding: 80px 0;
  background: linear-gradient(135deg, #fef9e7 0%, #fff8e1 100%);
  position: relative;
}

.service-details::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: radial-gradient(
      circle at 20% 80%,
      rgba(255, 193, 7, 0.1) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 80% 20%,
      rgba(255, 87, 34, 0.1) 0%,
      transparent 50%
    );
  pointer-events: none;
}

.service-details .container {
  position: relative;
  z-index: 2;
}

/* Service Details Content */
.service-details-content {
  background: #ffffff;
  border-radius: 20px;
  padding: 40px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

.service-details-content::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 5px;
  background: linear-gradient(
    90deg,
    #ff6b35,
    #f7931e,
    #ffc107,
    #4caf50,
    #2196f3,
    #9c27b0
  );
}

/* Service Hero Image */
.service-hero-image {
  margin-bottom: 30px;
  border-radius: 15px;
  overflow: hidden;
  position: relative;
}

.service-hero-image img {
  width: 100%;
  height: 400px;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.service-hero-image:hover img {
  transform: scale(1.05);
}

.service-badge {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 25px;
  padding: 8px 20px;
  font-weight: 600;
  color: #333;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

/* Service Content Typography */
.service-details-content h2 {
  color: #2c3e50;
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 25px;
  position: relative;
  padding-bottom: 15px;
}

.service-details-content h2::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 80px;
  height: 4px;
  background: linear-gradient(90deg, #ff6b35, #ffc107);
  border-radius: 2px;
}

.service-details-content h3 {
  color: #34495e;
  font-size: 1.8rem;
  font-weight: 600;
  margin: 30px 0 20px 0;
  position: relative;
  padding-left: 20px;
}

.service-details-content h3::before {
  content: "";
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 25px;
  background: linear-gradient(180deg, #ff6b35, #ffc107);
  border-radius: 2px;
}

.service-details-content p {
  color: #555;
  font-size: 1.1rem;
  line-height: 1.8;
  margin-bottom: 20px;
}

.service-details-content .lead {
  font-size: 1.25rem;
  font-weight: 500;
  color: #2c3e50;
  margin-bottom: 30px;
}

/* Feature Cards */
.feature-card {
  background: #ffffff;
  border: 2px solid #f8f9fa;
  border-radius: 15px;
  padding: 30px 20px;
  text-align: center;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  margin-bottom: 20px;
}

.feature-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 193, 7, 0.1),
    transparent
  );
  transition: left 0.5s ease;
}

.feature-card:hover::before {
  left: 100%;
}

.feature-card:hover {
  transform: translateY(-10px);
  border-color: #ffc107;
  box-shadow: 0 15px 40px rgba(255, 193, 7, 0.2);
}

.feature-card i {
  font-size: 3rem;
  margin-bottom: 20px;
  background: linear-gradient(135deg, #ff6b35, #ffc107);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.feature-card h4 {
  color: #2c3e50;
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 15px;
}

.feature-card p {
  color: #666;
  font-size: 1rem;
  margin-bottom: 0;
  line-height: 1.2;
}
/* Enhanced Lists */
.service-details-content ul {
  list-style: none;
  padding: 0;
  margin: 25px 0;
}

.service-details-content ul li {
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
  position: relative;
  padding-left: 40px;
  color: #555;
  font-size: 1.05rem;
  transition: all 0.3s ease;
}

.service-details-content ul li:last-child {
  border-bottom: none;
}

.service-details-content ul li:hover {
  color: #2c3e50;
  padding-left: 45px;
}

.service-details-content ul li i {
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  font-size: 1.2rem;
  color: #28a745;
  background: rgba(40, 167, 69, 0.1);
  width: 25px;
  height: 25px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Indian Cultural Elements */
.indian-pattern {
  position: relative;
}

.indian-pattern::before {
  content: "";
  position: absolute;
  top: -10px;
  left: -10px;
  right: -10px;
  bottom: -10px;
  background-image: radial-gradient(
      circle at 25% 25%,
      #ff6b35 2px,
      transparent 2px
    ),
    radial-gradient(circle at 75% 75%, #ffc107 2px, transparent 2px);
  background-size: 20px 20px;
  opacity: 0.1;
  z-index: -1;
}

/* Service Highlights */
.service-highlights {
  background: linear-gradient(135deg, #fff3e0 0%, #fce4ec 100%);
  border-radius: 15px;
  padding: 30px;
  margin: 30px 0;
  position: relative;
  overflow: hidden;
}

.service-highlights::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #ff6b35, #ffc107, #4caf50);
}

.service-highlights h4 {
  color: #d84315;
  font-size: 1.4rem;
  font-weight: 600;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
}

.service-highlights h4 i {
  margin-right: 10px;
  font-size: 1.5rem;
  color: #ff6b35;
}

/* Pricing Cards */
.pricing-card {
  background: #ffffff;
  border-radius: 20px;
  padding: 30px;
  text-align: center;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  border: 2px solid transparent;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.pricing-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #ff6b35, #ffc107);
}

.pricing-card:hover {
  transform: translateY(-10px);
  border-color: #ffc107;
  box-shadow: 0 20px 50px rgba(255, 193, 7, 0.2);
}

.pricing-card .price {
  font-size: 2.5rem;
  font-weight: 700;
  color: #2c3e50;
  margin: 20px 0;
}

.pricing-card .price span {
  font-size: 1rem;
  color: #666;
  font-weight: 400;
}

/* Call to Action in Service Details */
.service-cta {
  background: linear-gradient(135deg, #ff6b35 0%, #ffc107 100%);
  border-radius: 20px;
  padding: 40px;
  text-align: center;
  margin: 40px 0;
  color: white;
  position: relative;
  overflow: hidden;
}

.service-cta::before {
  content: "";
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(
    circle,
    rgba(255, 255, 255, 0.1) 0%,
    transparent 70%
  );
  animation: rotate 20s linear infinite;
}

@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.service-cta h3 {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 15px;
  position: relative;
  z-index: 2;
}

.service-cta p {
  font-size: 1.1rem;
  margin-bottom: 25px;
  opacity: 0.9;
  position: relative;
  z-index: 2;
}

.service-cta .btn {
  background: rgba(255, 255, 255, 0.2);
  border: 2px solid white;
  color: white;
  padding: 12px 30px;
  font-weight: 600;
  border-radius: 50px;
  transition: all 0.3s ease;
  position: relative;
  z-index: 2;
}

.service-cta .btn:hover {
  background: white;
  color: #ff6b35;
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}
/* Sidebar Styles for Service Details */
.service-sidebar {
  background: #ffffff;
  border-radius: 20px;
  padding: 30px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 100px;
}

.service-sidebar h4 {
  color: #2c3e50;
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 25px;
  padding-bottom: 15px;
  border-bottom: 2px solid #f8f9fa;
  position: relative;
}

.service-sidebar h4::after {
  content: "";
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 50px;
  height: 2px;
  background: linear-gradient(90deg, #ff6b35, #ffc107);
}

.service-info-item {
  display: flex;
  align-items: center;
  padding: 15px 0;
  border-bottom: 1px solid #f0f0f0;
}

.service-info-item:last-child {
  border-bottom: none;
}

.service-info-item i {
  font-size: 1.2rem;
  color: #ff6b35;
  margin-right: 15px;
  width: 20px;
  text-align: center;
}

.service-info-item span {
  color: #555;
  font-weight: 500;
}

/* Enhanced Gallery Grid for Service Details */
.service-gallery {
  margin: 60px 0;
  position: relative;
}

.service-gallery::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(
      circle at 20% 20%,
      rgba(255, 107, 53, 0.05) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 80% 80%,
      rgba(255, 193, 7, 0.05) 0%,
      transparent 50%
    );
  pointer-events: none;
}

.service-gallery .section-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 15px;
  position: relative;
  display: inline-block;
}

.service-gallery .section-title::after {
  content: "";
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(90deg, #ff6b35, #ffc107);
  border-radius: 2px;
  animation: titleUnderline 2s ease-in-out infinite alternate;
}

@keyframes titleUnderline {
  0% {
    width: 60%;
  }
  100% {
    width: 100%;
  }
}

.service-gallery .lead {
  font-size: 1.2rem;
  color: #666;
  margin-bottom: 50px;
}

.gallery-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 25px;
  margin-top: 40px;
  perspective: 1000px;
}

.gallery-item {
  position: relative;
  border-radius: 20px;
  overflow: hidden;
  aspect-ratio: 4/3;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  transform-style: preserve-3d;
  animation: galleryItemFloat 6s ease-in-out infinite;
}

.gallery-item:nth-child(1) {
  animation-delay: 0s;
}
.gallery-item:nth-child(2) {
  animation-delay: 1s;
}
.gallery-item:nth-child(3) {
  animation-delay: 2s;
}
.gallery-item:nth-child(4) {
  animation-delay: 3s;
}
.gallery-item:nth-child(5) {
  animation-delay: 4s;
}
.gallery-item:nth-child(6) {
  animation-delay: 5s;
}

@keyframes galleryItemFloat {
  0%,
  100% {
    transform: translateY(0px) rotateX(0deg);
  }
  50% {
    transform: translateY(-10px) rotateX(2deg);
  }
}

.gallery-item:hover {
  transform: translateY(-15px) rotateX(5deg) rotateY(5deg) scale(1.02);
  box-shadow: 0 25px 50px rgba(255, 107, 53, 0.2);
  animation-play-state: paused;
}

.gallery-item img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.4s ease;
  filter: brightness(0.9) contrast(1.1);
}

.gallery-item:hover img {
  transform: scale(1.1);
  filter: brightness(1) contrast(1.2);
}

.gallery-item .overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(255, 107, 53, 0.9),
    rgba(255, 193, 7, 0.9)
  );
  opacity: 0;
  transition: all 0.4s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(5px);
}

.gallery-item:hover .overlay {
  opacity: 1;
}

.gallery-item .overlay i {
  font-size: 3rem;
  color: white;
  margin-bottom: 15px;
  transform: scale(0.5) rotate(180deg);
  transition: all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.gallery-item:hover .overlay i {
  transform: scale(1) rotate(0deg);
}

.gallery-item .overlay .gallery-title {
  color: white;
  font-size: 1.2rem;
  font-weight: 600;
  text-align: center;
  margin-bottom: 8px;
  transform: translateY(20px);
  opacity: 0;
  transition: all 0.4s ease 0.1s;
}

.gallery-item:hover .overlay .gallery-title {
  transform: translateY(0);
  opacity: 1;
}

.gallery-item .overlay .gallery-subtitle {
  color: rgba(255, 255, 255, 0.9);
  font-size: 0.9rem;
  text-align: center;
  transform: translateY(20px);
  opacity: 0;
  transition: all 0.4s ease 0.2s;
}

.gallery-item:hover .overlay .gallery-subtitle {
  transform: translateY(0);
  opacity: 1;
}
/* Gallery Item Decorative Elements */
.gallery-item::before {
  content: "";
  position: absolute;
  top: 15px;
  right: 15px;
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  backdrop-filter: blur(10px);
  z-index: 2;
  opacity: 0;
  transform: scale(0);
  transition: all 0.3s ease;
}

.gallery-item:hover::before {
  opacity: 1;
  transform: scale(1);
}

.gallery-item::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #ff6b35, #ffc107, #4caf50);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.gallery-item:hover::after {
  transform: scaleX(1);
}

/* Gallery Filter Buttons */
.gallery-filters {
  display: flex;
  justify-content: center;
  gap: 15px;
  margin-bottom: 40px;
  flex-wrap: wrap;
}

.gallery-filter-btn {
  padding: 10px 25px;
  background: transparent;
  border: 2px solid #ff6b35;
  color: #ff6b35;
  border-radius: 25px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  text-decoration: none;
}

.gallery-filter-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, #ff6b35, #ffc107);
  transition: left 0.3s ease;
  z-index: -1;
}

.gallery-filter-btn:hover::before,
.gallery-filter-btn.active::before {
  left: 0;
}

.gallery-filter-btn:hover,
.gallery-filter-btn.active {
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(255, 107, 53, 0.3);
}

/* Gallery Loading Animation */
.gallery-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.gallery-spinner {
  width: 50px;
  height: 50px;
  border: 4px solid rgba(255, 107, 53, 0.2);
  border-top: 4px solid #ff6b35;
  border-radius: 50%;
  animation: gallerySpinner 1s linear infinite;
}

@keyframes gallerySpinner {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Gallery Modal Styles */
.gallery-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.9);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.gallery-modal.active {
  opacity: 1;
  visibility: visible;
}

.gallery-modal-content {
  max-width: 90%;
  max-height: 90%;
  position: relative;
  animation: modalZoomIn 0.3s ease;
}

@keyframes modalZoomIn {
  0% {
    transform: scale(0.5);
    opacity: 0;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.gallery-modal img {
  width: 100%;
  height: auto;
  border-radius: 10px;
}

.gallery-modal-close {
  position: absolute;
  top: -40px;
  right: 0;
  color: white;
  font-size: 2rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.gallery-modal-close:hover {
  color: #ff6b35;
  transform: scale(1.2);
}
/* Enhanced Gallery Page Styles */
.gallery-page {
  overflow-x: hidden;
}

.gallery-hero-section {
  min-height: 100vh;
  display: flex;
  align-items: center;
  position: relative;
}

.gallery-hero-section::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(
      circle at 30% 30%,
      rgba(255, 255, 255, 0.1) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 70% 70%,
      rgba(255, 193, 7, 0.1) 0%,
      transparent 50%
    );
  pointer-events: none;
}

.gallery-badge {
  display: inline-block;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  padding: 12px 24px;
  border-radius: 50px;
  font-weight: 600;
  font-size: 0.9rem;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  margin-bottom: 2rem;
  animation: badgeFloat 3s ease-in-out infinite alternate;
}

@keyframes badgeFloat {
  0% {
    transform: translateY(0px);
  }
  100% {
    transform: translateY(-10px);
  }
}

.gallery-stats .stat-item {
  text-align: center;
  padding: 20px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 15px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.gallery-stats .stat-item:hover {
  transform: translateY(-5px);
  background: rgba(255, 255, 255, 0.2);
}

.section-badge {
  display: inline-block;
  background: linear-gradient(135deg, #ff6b35, #ffc107);
  color: white;
  padding: 8px 20px;
  border-radius: 25px;
  font-weight: 600;
  font-size: 0.85rem;
  margin-bottom: 1rem;
}

/* Gallery Navigation Buttons */
.gallery-navigation {
  margin-bottom: 3rem;
}

.gallery-nav-btn {
  width: 100%;
  background: white;
  border: 2px solid #e9ecef;
  border-radius: 15px;
  padding: 20px 15px;
  text-align: center;
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.gallery-nav-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #ff6b35, #ffc107);
  transition: left 0.4s ease;
  z-index: 1;
}

.gallery-nav-btn:hover::before,
.gallery-nav-btn.active::before {
  left: 0;
}

.gallery-nav-btn i,
.gallery-nav-btn span,
.gallery-nav-btn small {
  position: relative;
  z-index: 2;
  transition: color 0.3s ease;
}

.gallery-nav-btn i {
  font-size: 1.5rem;
  color: #ff6b35;
  margin-bottom: 8px;
  display: block;
}

.gallery-nav-btn span {
  font-weight: 600;
  color: #2c3e50;
  display: block;
  margin-bottom: 4px;
}

.gallery-nav-btn small {
  color: #6c757d;
  font-size: 0.75rem;
}

.gallery-nav-btn:hover,
.gallery-nav-btn.active {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(255, 107, 53, 0.3);
  border-color: #ff6b35;
}

.gallery-nav-btn:hover i,
.gallery-nav-btn:hover span,
.gallery-nav-btn:hover small,
.gallery-nav-btn.active i,
.gallery-nav-btn.active span,
.gallery-nav-btn.active small {
  color: white;
}

/* Creative Masonry Gallery Grid */
.creative-gallery-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  grid-auto-rows: 200px;
  gap: 20px;
  grid-auto-flow: dense;
}

.gallery-item {
  position: relative;
  border-radius: 20px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.gallery-item.large {
  grid-row: span 2;
  grid-column: span 2;
}

.gallery-item.medium {
  grid-row: span 2;
}

.gallery-item.small {
  grid-row: span 1;
}

.gallery-item img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.4s ease;
  filter: brightness(0.9) contrast(1.1);
}

.gallery-item:hover {
  transform: translateY(-10px) scale(1.02);
  box-shadow: 0 25px 50px rgba(255, 107, 53, 0.2);
}

.gallery-item:hover img {
  transform: scale(1.1);
  filter: brightness(1) contrast(1.2);
}

/* Gallery Overlay */
.gallery-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(255, 107, 53, 0.9),
    rgba(255, 193, 7, 0.9)
  );
  opacity: 0;
  transition: all 0.4s ease;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 20px;
  backdrop-filter: blur(5px);
}

.gallery-item:hover .gallery-overlay {
  opacity: 1;
}

.overlay-content {
  color: white;
}

.overlay-content h4 {
  font-size: 1.3rem;
  font-weight: 700;
  margin-bottom: 8px;
  transform: translateY(20px);
  opacity: 0;
  transition: all 0.4s ease 0.1s;
}

.overlay-content p {
  font-size: 0.9rem;
  margin-bottom: 15px;
  transform: translateY(20px);
  opacity: 0;
  transition: all 0.4s ease 0.2s;
}

.category-tag {
  display: inline-block;
  background: rgba(255, 255, 255, 0.2);
  padding: 4px 12px;
  border-radius: 15px;
  font-size: 0.75rem;
  font-weight: 600;
  backdrop-filter: blur(10px);
  transform: translateY(20px);
  opacity: 0;
  transition: all 0.4s ease 0.3s;
}

.gallery-item:hover .overlay-content h4,
.gallery-item:hover .overlay-content p,
.gallery-item:hover .category-tag {
  transform: translateY(0);
  opacity: 1;
}

.overlay-actions {
  display: flex;
  gap: 10px;
  align-self: flex-end;
}

.gallery-zoom,
.gallery-share {
  width: 45px;
  height: 45px;
  border: none;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  transform: scale(0);
}

.gallery-item:hover .gallery-zoom,
.gallery-item:hover .gallery-share {
  transform: scale(1);
}

.gallery-zoom:hover,
.gallery-share:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}
/* Gallery Responsive Styles */
@media (max-width: 1200px) {
  .gallery-item.large {
    grid-column: span 1;
  }

  .creative-gallery-grid {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  }
}

@media (max-width: 768px) {
  .gallery-hero-section {
    min-height: 80vh;
    padding: 60px 0;
  }

  .gallery-hero-section h1 {
    font-size: 2.5rem;
  }

  .gallery-stats .stat-item {
    padding: 15px;
  }

  .gallery-nav-btn {
    padding: 15px 10px;
  }

  .gallery-nav-btn i {
    font-size: 1.2rem;
  }

  .gallery-nav-btn span {
    font-size: 0.9rem;
  }

  .creative-gallery-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
  }

  .gallery-item.large,
  .gallery-item.medium {
    grid-row: span 1;
    grid-column: span 1;
  }

  .gallery-overlay {
    padding: 15px;
  }

  .overlay-content h4 {
    font-size: 1.1rem;
  }

  .overlay-content p {
    font-size: 0.8rem;
  }
}

@media (max-width: 480px) {
  .gallery-hero-section h1 {
    font-size: 2rem;
  }

  .gallery-hero-section .lead {
    font-size: 1rem;
  }

  .gallery-stats {
    margin-bottom: 2rem;
  }

  .gallery-navigation .row {
    gap: 10px;
  }

  .gallery-nav-btn {
    padding: 12px 8px;
  }

  .creative-gallery-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .gallery-overlay {
    padding: 12px;
  }

  .gallery-zoom,
  .gallery-share {
    width: 40px;
    height: 40px;
    font-size: 1rem;
  }
}

/* Gallery Modal Enhancements */
.modal-overlay-info {
  background: linear-gradient(
    to top,
    rgba(0, 0, 0, 0.8) 0%,
    rgba(0, 0, 0, 0.4) 50%,
    transparent 100%
  );
}

.modal-overlay-info h5 {
  font-size: 1.3rem;
  font-weight: 700;
  margin-bottom: 8px;
}

.modal-overlay-info p {
  font-size: 0.95rem;
  opacity: 0.9;
}

/* Load More Button Styles */
.btn-outline-primary.disabled {
  background: #28a745;
  border-color: #28a745;
  color: white;
}

/* Gallery Animation Classes */
.gallery-item-enter {
  opacity: 0;
  transform: translateY(30px) scale(0.8);
}

.gallery-item-enter-active {
  opacity: 1;
  transform: translateY(0) scale(1);
  transition: all 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.gallery-item-exit {
  opacity: 1;
  transform: translateY(0) scale(1);
}

.gallery-item-exit-active {
  opacity: 0;
  transform: translateY(-30px) scale(0.8);
  transition: all 0.4s ease;
}

/* Gallery Loading States */
.gallery-loading-skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Gallery Hover Effects Enhancement */
.gallery-item::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(
    circle at center,
    rgba(255, 255, 255, 0.1) 0%,
    transparent 70%
  );
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.gallery-item:hover::after {
  opacity: 1;
}

/* Gallery Category Indicators */
.gallery-item[data-category="wedding"]::before {
  content: "💒";
  position: absolute;
  top: 15px;
  left: 15px;
  font-size: 1.2rem;
  z-index: 3;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.gallery-item[data-category="birthday"]::before {
  content: "🎂";
  position: absolute;
  top: 15px;
  left: 15px;
  font-size: 1.2rem;
  z-index: 3;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.gallery-item[data-category="naming"]::before {
  content: "⭐";
  position: absolute;
  top: 15px;
  left: 15px;
  font-size: 1.2rem;
  z-index: 3;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.gallery-item[data-category="corporate"]::before {
  content: "🏢";
  position: absolute;
  top: 15px;
  left: 15px;
  font-size: 1.2rem;
  z-index: 3;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.gallery-item[data-category="special"]::before {
  content: "📸";
  position: absolute;
  top: 15px;
  left: 15px;
  font-size: 1.2rem;
  z-index: 3;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.gallery-item:hover::before {
  opacity: 1;
}

/* Testimonial Section in Service Details */
.service-testimonial {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 20px;
  padding: 40px;
  margin: 40px 0;
  text-align: center;
  position: relative;
}

.service-testimonial::before {
  content: '"';
  position: absolute;
  top: -10px;
  left: 50%;
  transform: translateX(-50%);
  font-size: 4rem;
  color: #ff6b35;
  font-family: serif;
  line-height: 1;
}

.service-testimonial p {
  font-size: 1.2rem;
  font-style: italic;
  color: #555;
  margin-bottom: 25px;
  line-height: 1.6;
}

.service-testimonial .client-info {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15px;
}

.service-testimonial .client-avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  object-fit: cover;
  border: 3px solid #ff6b35;
}

.service-testimonial .client-details h5 {
  color: #2c3e50;
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 5px;
}

.service-testimonial .client-details span {
  color: #666;
  font-size: 0.9rem;
}

/* Process Steps */
.process-steps {
  margin: 50px 0;
}

.step-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 30px;
  position: relative;
}

.step-item:not(:last-child)::after {
  content: "";
  position: absolute;
  left: 25px;
  top: 60px;
  width: 2px;
  height: 40px;
  background: linear-gradient(180deg, #ff6b35, #ffc107);
}

.step-number {
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, #ff6b35, #ffc107);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 700;
  font-size: 1.2rem;
  margin-right: 20px;
  flex-shrink: 0;
}

.step-content h5 {
  color: #2c3e50;
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 10px;
}

.step-content p {
  color: #666;
  margin-bottom: 0;
  line-height: 1.6;
}
/* Responsive Design for Service Details */
@media (max-width: 992px) {
  .service-details-content {
    padding: 30px;
  }

  .service-details-content h2 {
    font-size: 2rem;
  }

  .service-sidebar {
    position: static;
    margin-top: 30px;
  }

  .feature-card {
    margin-bottom: 20px;
  }

  .service-hero-image img {
    height: 300px;
  }
}

@media (max-width: 768px) {
  .service-details {
    padding: 60px 0;
  }

  .service-details-content {
    padding: 25px;
    border-radius: 15px;
  }

  .service-details-content h2 {
    font-size: 1.8rem;
  }

  .service-details-content h3 {
    font-size: 1.5rem;
  }

  .feature-card {
    padding: 20px 15px;
  }

  .feature-card i {
    font-size: 2.5rem;
  }

  .service-cta {
    padding: 30px 20px;
  }

  .service-cta h3 {
    font-size: 1.6rem;
  }

  .service-hero-image img {
    height: 250px;
  }

  .gallery-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
  }

  .service-testimonial {
    padding: 30px 20px;
  }

  .service-testimonial .client-info {
    flex-direction: column;
    gap: 10px;
  }

  .step-item {
    flex-direction: column;
    text-align: center;
  }

  .step-item:not(:last-child)::after {
    display: none;
  }

  .step-number {
    margin: 0 auto 15px auto;
  }
}

@media (max-width: 576px) {
  .service-details-content {
    padding: 20px;
  }

  .service-details-content h2 {
    font-size: 1.6rem;
  }

  .service-details-content .lead {
    font-size: 1.1rem;
  }

  .feature-card {
    padding: 15px;
  }

  .service-cta {
    padding: 25px 15px;
  }

  .service-hero-image img {
    height: 200px;
  }

  .gallery-grid {
    grid-template-columns: 1fr;
  }

  .service-highlights {
    padding: 20px;
  }

  .pricing-card {
    padding: 20px;
  }
}
/* Additional Mobile Optimizations */
@media (max-width: 480px) {
  .service-details {
    padding: 40px 0;
  }

  .service-details-content {
    padding: 15px;
  }

  .service-details-content h2 {
    font-size: 1.4rem;
    line-height: 1.3;
  }

  .service-details-content .lead {
    font-size: 1rem;
  }

  .feature-card {
    padding: 15px 10px;
    text-align: center;
  }

  .feature-card i {
    font-size: 2rem;
  }

  .feature-card h4 {
    font-size: 1.1rem;
  }

  .service-hero-image img {
    height: 180px;
  }

  .service-badge {
    position: static !important;
    display: inline-block;
    margin: 10px 0;
  }

  .service-cta {
    padding: 20px 15px;
  }

  .service-cta h3 {
    font-size: 1.4rem;
  }

  .service-sidebar {
    padding: 20px;
  }

  .theme-card {
    margin-bottom: 15px;
  }

  .step-content h5 {
    font-size: 1.1rem;
  }

  .service-testimonial {
    padding: 20px 15px;
  }

  .service-testimonial p {
    font-size: 1rem;
  }

  .gallery-grid {
    gap: 10px;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  }

  .gallery-filters {
    gap: 10px;
  }

  .gallery-filter-btn {
    padding: 8px 20px;
    font-size: 0.9rem;
  }

  .service-gallery .section-title {
    font-size: 2rem;
  }

  .gallery-item .overlay .gallery-title {
    font-size: 1rem;
  }

  .gallery-item .overlay .gallery-subtitle {
    font-size: 0.8rem;
  }

  .page-title h1 {
    font-size: 1.8rem;
    line-height: 1.2;
  }

  .page-title p {
    font-size: 0.9rem;
  }
}

/* Print Styles */
@media print {
  .service-details {
    background: white !important;
  }

  .service-details-content {
    box-shadow: none !important;
    border: 1px solid #ddd;
  }

  .service-cta,
  .service-sidebar,
  .gallery-grid {
    display: none !important;
  }

  .service-hero-image img {
    max-height: 200px;
  }
}

/* High DPI Displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .service-hero-image img {
    image-rendering: -webkit-optimize-contrast;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .service-details {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
  }

  .service-details-content {
    background: #333;
    color: #fff;
  }

  .service-details-content h2,
  .service-details-content h3 {
    color: #fff;
  }

  .feature-card {
    background: #444 !important;
    color: #fff;
    border-color: #555;
  }

  .service-highlights {
    background: linear-gradient(135deg, #2d2d2d 0%, #3d3d3d 100%);
  }

  .service-sidebar {
    background: #333;
    color: #fff;
  }

  .theme-card {
    background: #444;
    color: #fff;
    border-color: #555;
  }
}

.call-to-action .lead {
  font-size: 1.125rem;
  line-height: 1.6;
  font-weight: 400;
}

.call-to-action .stats-wrapper {
  background: var(--surface-color);
  border-radius: 16px;
  padding: 3rem 2rem;
  backdrop-filter: blur(10px);
  border: 1px solid color-mix(in srgb, var(--default-color), transparent 90%);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.08);
}

.call-to-action .stat-item {
  text-align: center;
  padding: 1rem 0;
}

.call-to-action .stat-item .stat-icon {
  width: 75px;
  height: 75px;
  background: color-mix(in srgb, var(--accent-color), transparent 90%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1rem;
  transition: all 0.3s ease;
}

.call-to-action .stat-item .stat-icon i {
  font-size: 2.5rem;
  color: var(--accent-color);
}

.call-to-action .stat-item .stat-number {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--heading-color);
  line-height: 1;
  margin-bottom: 0.5rem;
}

.call-to-action .cta-buttons .btn.btn-outline-secondary {
  color: var(--default-color);
  border-color: color-mix(in srgb, var(--default-color), transparent 70%);
  background: transparent;
}

.call-to-action .cta-buttons .btn.btn-outline-secondary:hover {
  background: var(--accent-color);
  border-color: var(--accent-color);
  color: var(--contrast-color);
  transform: translateY(-2px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
}

.call-to-action .security-note {
  border-top: 1px solid
    color-mix(in srgb, var(--default-color), transparent 90%);
  padding-top: 1rem;
}

.call-to-action .security-note small {
  display: inline-flex;
  align-items: center;
  font-size: 0.875rem;
  color: color-mix(in srgb, var(--default-color), transparent 40%);
}

.call-to-action .security-note small i {
  color: var(--accent-color);
}

@media (max-width: 768px) {
  .call-to-action .display-4 {
    font-size: 2rem;
  }

  .call-to-action .stats-wrapper {
    padding: 2rem 1rem;
  }

  .call-to-action .stat-item {
    margin-bottom: 2rem;
  }

  .call-to-action .stat-item .stat-number {
    font-size: 2rem;
  }

  .call-to-action .cta-buttons .btn {
    display: block;
    width: 100%;
    margin-bottom: 1rem;
  }

  .call-to-action .cta-buttons .btn:last-child {
    margin-bottom: 0;
  }
}

/*--------------------------------------------------------------
  # Testimonials Section
  --------------------------------------------------------------*/
.testimonials {
  position: relative;
  overflow: hidden;
  /* Swiper Navigation */
  /* Swiper Pagination */
  /* Responsive Styles */
}

.testimonials .testimonial-slider {
  position: relative;
  padding-bottom: 50px;
}

.testimonials .testimonial-slider .swiper-wrapper {
  height: auto !important;
}

.testimonials .testimonial-item {
  background: linear-gradient(
    135deg,
    var(--surface-color) 0%,
    color-mix(in srgb, var(--surface-color), var(--accent-color) 2%) 100%
  );
  border-radius: 20px;
  padding: 0;
  height: 100%;
  border: 1px solid color-mix(in srgb, var(--default-color), transparent 90%);
  transition: all 0.4s ease;
  overflow: hidden;
  position: relative;
}

.testimonials .testimonial-item::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(
    90deg,
    var(--accent-color),
    color-mix(in srgb, var(--accent-color), var(--heading-color) 30%)
  );
  transform: scaleX(0);
  transform-origin: left;
  transition: transform 0.4s ease;
}

.testimonials .testimonial-item:hover {
  border-color: var(--accent-color);
}

.testimonials .testimonial-item:hover::before {
  transform: scaleX(1);
}

.testimonials .testimonial-item:hover .testimonial-header img {
  transform: scale(1.05);
}

.testimonials .testimonial-item:hover .quote-icon {
  color: var(--accent-color);
  transform: scale(1.1);
}

.testimonials .testimonial-header {
  position: relative;
  text-align: center;
  padding: 30px 30px 20px;
  background: linear-gradient(
    135deg,
    color-mix(in srgb, var(--surface-color), var(--accent-color) 3%) 0%,
    var(--surface-color) 100%
  );
}

.testimonials .testimonial-header img {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  object-fit: cover;
  border: 4px solid color-mix(in srgb, var(--accent-color), transparent 70%);
  margin-bottom: 15px;
  transition: all 0.3s ease;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.testimonials .testimonial-header .rating {
  display: flex;
  justify-content: center;
  gap: 3px;
}

.testimonials .testimonial-header .rating i {
  color: #ffc107;
  font-size: 0.9rem;
}

.testimonials .testimonial-body {
  padding: 0 30px 20px;
}

.testimonials .testimonial-body p {
  font-size: 1rem;
  line-height: 1.6;
  color: color-mix(in srgb, var(--default-color), transparent 20%);
  margin: 0;
  font-style: italic;
  text-align: center;
  position: relative;
}

.testimonials .testimonial-body p::before,
.testimonials .testimonial-body p::after {
  content: '"';
  font-size: 1.5rem;
  color: var(--accent-color);
  opacity: 0.6;
  font-family: serif;
  position: absolute;
}

.testimonials .testimonial-body p::before {
  top: -5px;
  left: -10px;
}

.testimonials .testimonial-body p::after {
  bottom: -20px;
  right: -5px;
}

.testimonials .testimonial-footer {
  padding: 20px 30px 30px;
  text-align: center;
  position: relative;
}

.testimonials .testimonial-footer h5 {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--heading-color);
  margin: 0 0 5px;
}

.testimonials .testimonial-footer span {
  font-size: 0.85rem;
  color: color-mix(in srgb, var(--default-color), transparent 40%);
  display: block;
  margin-bottom: 15px;
}

.testimonials .testimonial-footer .quote-icon {
  position: absolute;
  bottom: 15px;
  right: 25px;
  color: color-mix(in srgb, var(--accent-color), transparent 60%);
  font-size: 1.5rem;
  transition: all 0.3s ease;
}

.testimonials .swiper-navigation {
  position: relative;
  margin-top: 25px;
  display: flex;
  justify-content: flex-end;
}

.testimonials .swiper-button-prev,
.testimonials .swiper-button-next {
  position: static;
  width: 45px;
  height: 45px;
  margin: 0 10px;
  background: var(--accent-color);
  border-radius: 50%;
  color: var(--contrast-color);
  font-size: 16px;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.testimonials .swiper-button-prev:hover,
.testimonials .swiper-button-next:hover {
  background: color-mix(in srgb, var(--accent-color), var(--heading-color) 20%);
  transform: scale(1.05);
}

.testimonials .swiper-button-prev::after,
.testimonials .swiper-button-next::after {
  font-size: 16px;
  font-weight: 600;
}

.testimonials .swiper-pagination {
  position: static;
  margin-top: 30px;
  text-align: center;
}

.testimonials .swiper-pagination .swiper-pagination-bullet {
  width: 12px;
  height: 12px;
  background: color-mix(in srgb, var(--default-color), transparent 70%);
  opacity: 1;
  margin: 0 6px;
  transition: all 0.3s ease;
}

.testimonials
  .swiper-pagination
  .swiper-pagination-bullet.swiper-pagination-bullet-active {
  background: var(--accent-color);
  transform: scale(1.2);
}

@media (max-width: 1199px) {
  .testimonials .testimonial-item .testimonial-header {
    padding: 25px 25px 15px;
  }

  .testimonials .testimonial-item .testimonial-header img {
    width: 70px;
    height: 70px;
  }

  .testimonials .testimonial-item .testimonial-body,
  .testimonials .testimonial-item .testimonial-footer {
    padding-left: 25px;
    padding-right: 25px;
  }
}

@media (max-width: 991px) {
  .testimonials .testimonial-item {
    margin-bottom: 30px;
  }
}

@media (max-width: 767px) {
  .testimonials .testimonial-item .testimonial-header {
    padding: 20px 20px 10px;
  }

  .testimonials .testimonial-item .testimonial-header img {
    width: 60px;
    height: 60px;
  }

  .testimonials .testimonial-item .testimonial-header .rating i {
    font-size: 0.8rem;
  }

  .testimonials .testimonial-item .testimonial-body {
    padding: 0 20px 15px;
  }

  .testimonials .testimonial-item .testimonial-body p {
    font-size: 0.95rem;
  }

  .testimonials .testimonial-item .testimonial-footer {
    padding: 15px 20px 20px;
  }

  .testimonials .testimonial-item .testimonial-footer h5 {
    font-size: 1rem;
  }

  .testimonials .testimonial-item .testimonial-footer span {
    font-size: 0.8rem;
  }

  .testimonials .testimonial-item .testimonial-footer .quote-icon {
    font-size: 1.3rem;
    bottom: 10px;
    right: 15px;
  }

  .testimonials .swiper-button-prev,
  .testimonials .swiper-button-next {
    width: 40px;
    height: 40px;
    font-size: 14px;
  }

  .testimonials .swiper-button-prev::after,
  .testimonials .swiper-button-next::after {
    font-size: 14px;
  }
}

@media (max-width: 575px) {
  .testimonials .testimonial-slider {
    padding-bottom: 30px;
  }

  .testimonials .testimonial-item .testimonial-header {
    padding: 15px 15px 10px;
  }

  .testimonials .testimonial-item .testimonial-header img {
    width: 55px;
    height: 55px;
  }

  .testimonials .testimonial-item .testimonial-body {
    padding: 0 15px 10px;
  }

  .testimonials .testimonial-item .testimonial-body p {
    font-size: 0.9rem;
  }

  .testimonials .testimonial-item .testimonial-footer {
    padding: 10px 15px 15px;
  }

  .testimonials .testimonial-item .testimonial-footer h5 {
    font-size: 0.95rem;
  }

  .testimonials .testimonial-item .testimonial-footer .quote-icon {
    font-size: 1.2rem;
  }

  .testimonials .swiper-navigation {
    margin-top: 10px;
  }
}

/*--------------------------------------------------------------
  # Gallery Section
  --------------------------------------------------------------*/
.gallery .section-title h2 {
  font-size: 2.5rem;
  font-weight: 700;
  color: #333;
  margin-bottom: 1rem;
}

.gallery .section-title p {
  color: #666;
  font-size: 1.1rem;
  max-width: 600px;
  margin: 0 auto;
}

/* Filter Buttons */
.gallery-filters {
  margin-bottom: 3rem;
}

.filter-btn {
  background: #f8f9fa;
  border: 2px solid #e9ecef;
  color: #666;
  padding: 12px 24px;
  margin: 0 8px;
  border-radius: 25px;
  font-weight: 500;
  transition: all 0.3s ease;
  cursor: pointer;
  font-size: 14px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.filter-btn:hover,
.filter-btn.active {
  background: #ff6b35;
  border-color: #ff6b35;
  color: #fff;
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(255, 107, 53, 0.3);
}

/* Gallery Grid */
.gallery-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  grid-auto-rows: 250px;
  gap: 20px;
  padding: 0;
}

.gallery-item {
  position: relative;
  overflow: hidden;
  border-radius: 15px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

.gallery-item.large {
  grid-row: span 2;
}

.gallery-item.wide {
  grid-column: span 2;
}

.gallery-item.tall {
  grid-row: span 2;
}

.gallery-item img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.gallery-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: all 0.3s ease;
}

.gallery-overlay a {
  color: #fff;
  font-size: 2rem;
  text-decoration: none;
  transform: scale(0.8);
  transition: transform 0.3s ease;
}

.gallery-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
}

.gallery-item:hover img {
  transform: scale(1.1);
}

.gallery-item:hover .gallery-overlay {
  opacity: 1;
}

.gallery-item:hover .gallery-overlay a {
  transform: scale(1);
}

/* Filter Animation */
.gallery-item {
  animation: fadeIn 0.6s ease;
}

.gallery-item.hide {
  display: none;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .gallery-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    grid-auto-rows: 200px;
    gap: 15px;
  }

  .gallery-item.wide {
    grid-column: span 1;
  }

  .filter-btn {
    padding: 10px 20px;
    margin: 5px;
    font-size: 12px;
  }

  .gallery .section-title h2 {
    font-size: 2rem;
  }
}

@media (max-width: 480px) {
  .gallery-grid {
    grid-template-columns: 1fr;
    grid-auto-rows: 250px;
  }

  .gallery-item.large,
  .gallery-item.tall {
    grid-row: span 1;
  }
}

.glightbox-clean .gslide-description {
  background: #272727;
}

.glightbox-clean .gslide-title {
  color: rgba(255, 255, 255, 0.8);
  margin: 0;
}

/*--------------------------------------------------------------
# About Section
--------------------------------------------------------------*/
.about .content h3 {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
}

.about .content .lead {
  font-size: 1.1rem;
  color: color-mix(in srgb, var(--default-color), transparent 20%);
  margin-bottom: 1.5rem;
}

.about .content p {
  margin-bottom: 1.5rem;
  line-height: 1.7;
}

.about .content .quote-section {
  background-color: var(--surface-color);
  padding: 2rem;
  border-radius: 10px;
  border-left: 4px solid var(--accent-color);
  margin: 2rem 0;
}

.about .content .quote-section blockquote {
  margin: 0;
}

.about .content .quote-section blockquote p {
  font-style: italic;
  font-size: 1.1rem;
  margin-bottom: 1rem;
  color: var(--heading-color);
}

.about .content .quote-section blockquote cite {
  font-style: normal;
  font-weight: 600;
  color: var(--accent-color);
  font-size: 0.9rem;
}

.about .content .cta-buttons {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.about .content .cta-buttons .btn-primary,
.about .content .cta-buttons .btn-secondary {
  padding: 12px 30px;
  border-radius: 25px;
  font-weight: 600;
  text-decoration: none;
  transition: all 0.3s ease;
  border: 2px solid transparent;
  display: inline-block;
}

.about .content .cta-buttons .btn-primary {
  background-color: var(--accent-color);
  color: var(--contrast-color);
}

.about .content .cta-buttons .btn-primary:hover {
  background-color: color-mix(in srgb, var(--accent-color), black 15%);
  transform: translateY(-2px);
}

.about .content .cta-buttons .btn-secondary {
  background-color: transparent;
  color: var(--accent-color);
  border-color: var(--accent-color);
}

.about .content .cta-buttons .btn-secondary:hover {
  background-color: var(--accent-color);
  color: var(--contrast-color);
  transform: translateY(-2px);
}

.about .stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1.5rem;
}

.about .stats-grid .stat-card {
  background-color: var(--surface-color);
  padding: 2rem;
  border-radius: 15px;
  text-align: center;
  box-shadow: 0 5px 15px
    color-mix(in srgb, var(--default-color), transparent 90%);
  transition: all 0.3s ease;
}

.about .stats-grid .stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px
    color-mix(in srgb, var(--default-color), transparent 85%);
}

.about .stats-grid .stat-card .stat-icon {
  margin-bottom: 1rem;
}

.about .stats-grid .stat-card .stat-icon i {
  font-size: 2.5rem;
  color: var(--accent-color);
}

.about .stats-grid .stat-card .stat-content h4 {
  font-size: 1.8rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  color: var(--heading-color);
}

.about .stats-grid .stat-card .stat-content p {
  color: color-mix(in srgb, var(--default-color), transparent 30%);
  margin: 0;
  font-size: 0.9rem;
}

.about .audience-section {
  text-align: center;
}

.about .audience-section h3 {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 1rem;
}

.about .audience-section p {
  color: color-mix(in srgb, var(--default-color), transparent 20%);
  font-size: 1.1rem;
}

.about .audience-section .audience-item {
  text-align: center;
  padding: 1.5rem 1rem;
  border-radius: 10px;
  transition: all 0.3s ease;
}

.about .audience-section .audience-item:hover {
  background-color: var(--surface-color);
  transform: translateY(-5px);
}

.about .audience-section .audience-item:hover .audience-icon i {
  color: var(--accent-color);
  transform: scale(1.1);
}

.about .audience-section .audience-item .audience-icon {
  margin-bottom: 1rem;
}

.about .audience-section .audience-item .audience-icon i {
  font-size: 2rem;
  color: var(--heading-color);
  transition: all 0.3s ease;
}

.about .audience-section .audience-item h5 {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: var(--heading-color);
}

.about .audience-section .audience-item p {
  font-size: 0.85rem;
  color: color-mix(in srgb, var(--default-color), transparent 40%);
  margin: 0;
  line-height: 1.4;
}

@media (max-width: 992px) {
  .about .content {
    margin-bottom: 3rem;
  }

  .about .content .cta-buttons {
    justify-content: center;
  }

  .about .stats-grid {
    grid-template-columns: 1fr;
    max-width: 400px;
    margin: 0 auto;
  }
}

@media (max-width: 768px) {
  .about .content h3 {
    font-size: 1.8rem;
  }

  .about .content .quote-section {
    padding: 1.5rem;
  }

  .about .content .cta-buttons {
    flex-direction: column;
    align-items: center;
  }

  .about .content .cta-buttons .btn-primary,
  .about .content .cta-buttons .btn-secondary {
    width: 100%;
    max-width: 250px;
    text-align: center;
  }

  .about .audience-section .audience-item {
    padding: 1rem 0.5rem;
  }

  .about .audience-section .audience-item .audience-icon i {
    font-size: 1.8rem;
  }

  .about .audience-section .audience-item h5 {
    font-size: 0.9rem;
  }

  .about .audience-section .audience-item p {
    font-size: 0.8rem;
  }
}

/*--------------------------------------------------------------
# Speakers Section
--------------------------------------------------------------*/
.speakers .speakers-list .speaker-entry {
  margin-bottom: 2rem;
}

.speakers .speakers-list .speaker-profile {
  background: var(--surface-color);
  border: 1px solid color-mix(in srgb, var(--default-color), transparent 90%);
  border-radius: 12px;
  padding: 25px;
  transition: all 0.3s ease;
  height: 100%;
}

.speakers .speakers-list .speaker-profile:hover {
  border-color: var(--accent-color);
  box-shadow: 0 8px 30px
    color-mix(in srgb, var(--accent-color), transparent 92%);
  transform: translateY(-3px);
}

.speakers .speakers-list .speaker-profile:hover .speaker-photo img {
  transform: scale(1.05);
}

.speakers .speakers-list .speaker-meta {
  display: flex;
  align-items: flex-start;
  gap: 20px;
  margin-bottom: 20px;
}

.speakers .speakers-list .speaker-photo {
  flex-shrink: 0;
  width: 85px;
  height: 85px;
  border-radius: 50%;
  overflow: hidden;
  border: 3px solid color-mix(in srgb, var(--accent-color), transparent 80%);
}

.speakers .speakers-list .speaker-photo img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.speakers .speakers-list .speaker-info {
  flex-grow: 1;
}

.speakers .speakers-list .speaker-info h4 {
  font-size: 20px;
  font-weight: 700;
  margin-bottom: 6px;
  color: var(--heading-color);
}

.speakers .speakers-list .speaker-info .speaker-position {
  display: block;
  font-weight: 600;
  font-size: 15px;
  color: var(--accent-color);
  margin-bottom: 3px;
}

.speakers .speakers-list .speaker-info .speaker-org {
  display: block;
  font-size: 13px;
  color: color-mix(in srgb, var(--default-color), transparent 40%);
  margin-bottom: 10px;
}

.speakers .speakers-list .speaker-info .speaker-track {
  display: inline-block;
  background: linear-gradient(
    135deg,
    var(--accent-color),
    color-mix(in srgb, var(--accent-color), #000000 15%)
  );
  color: var(--contrast-color);
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  padding: 4px 10px;
  border-radius: 15px;
}

.speakers .speakers-list .speaker-details .speaker-topic {
  display: flex;
  align-items: flex-start;
  gap: 10px;
  margin-bottom: 15px;
  padding: 12px;
  background: color-mix(in srgb, var(--accent-color), transparent 95%);
  border-radius: 8px;
  border-left: 3px solid var(--accent-color);
}

.speakers .speakers-list .speaker-details .speaker-topic i {
  color: var(--accent-color);
  font-size: 16px;
  margin-top: 2px;
  flex-shrink: 0;
}

.speakers .speakers-list .speaker-details .speaker-topic span {
  font-weight: 600;
  font-size: 14px;
  color: var(--heading-color);
  line-height: 1.4;
}

.speakers .speakers-list .speaker-details .speaker-summary {
  font-size: 14px;
  line-height: 1.6;
  color: color-mix(in srgb, var(--default-color), transparent 25%);
  margin-bottom: 20px;
}

.speakers .speakers-list .speaker-details .speaker-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.speakers .speakers-list .speaker-details .speaker-actions .profile-btn {
  background: var(--accent-color);
  color: var(--contrast-color);
  padding: 8px 18px;
  border-radius: 25px;
  font-size: 13px;
  font-weight: 600;
  text-decoration: none;
  transition: all 0.3s ease;
}

.speakers .speakers-list .speaker-details .speaker-actions .profile-btn:hover {
  background: color-mix(in srgb, var(--accent-color), #000000 15%);
  transform: translateY(-2px);
}

.speakers .speakers-list .speaker-details .speaker-actions .speaker-social {
  display: flex;
  gap: 8px;
}

.speakers
  .speakers-list
  .speaker-details
  .speaker-actions
  .speaker-social
  .social-link {
  width: 32px;
  height: 32px;
  border: 1px solid color-mix(in srgb, var(--default-color), transparent 80%);
  color: var(--default-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  transition: all 0.3s ease;
}

.speakers
  .speakers-list
  .speaker-details
  .speaker-actions
  .speaker-social
  .social-link:hover {
  border-color: var(--accent-color);
  background: var(--accent-color);
  color: var(--contrast-color);
  transform: translateY(-2px);
}

@media (max-width: 992px) {
  .speakers .speakers-list .speaker-profile {
    padding: 20px;
  }

  .speakers .speakers-list .speaker-meta {
    gap: 15px;
  }

  .speakers .speakers-list .speaker-photo {
    width: 75px;
    height: 75px;
  }

  .speakers .speakers-list .speaker-info h4 {
    font-size: 18px;
  }
}

@media (max-width: 768px) {
  .speakers .speakers-list .speaker-entry {
    margin-bottom: 1.5rem;
  }

  .speakers .speakers-list .speaker-profile {
    padding: 18px;
  }

  .speakers .speakers-list .speaker-meta {
    flex-direction: column;
    align-items: center;
    text-align: center;
    gap: 15px;
  }

  .speakers .speakers-list .speaker-photo {
    width: 80px;
    height: 80px;
  }

  .speakers .speakers-list .speaker-details .speaker-actions {
    flex-direction: column;
    gap: 15px;
  }

  .speakers .speakers-list .speaker-details .speaker-actions .profile-btn {
    align-self: stretch;
    text-align: center;
  }
}

@media (max-width: 576px) {
  .speakers .speakers-list .speaker-profile {
    padding: 15px;
  }

  .speakers .speakers-list .speaker-info h4 {
    font-size: 17px;
  }

  .speakers .speakers-list .speaker-info .speaker-position {
    font-size: 14px;
  }

  .speakers .speakers-list .speaker-details .speaker-topic {
    padding: 10px;
  }

  .speakers .speakers-list .speaker-details .speaker-topic span {
    font-size: 13px;
  }

  .speakers .speakers-list .speaker-details .speaker-summary {
    font-size: 13px;
  }
}

/*--------------------------------------------------------------
# Speaker Details 2 Section
--------------------------------------------------------------*/
.speaker-details-2 .speaker-profile-card {
  background: var(--surface-color);
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 15px 40px
    color-mix(in srgb, var(--default-color), transparent 90%);
  margin-bottom: 2rem;
}

.speaker-details-2 .speaker-profile-card .profile-image-wrapper {
  position: relative;
}

.speaker-details-2 .speaker-profile-card .profile-image-wrapper img {
  width: 100%;
  height: 320px;
  object-fit: cover;
}

@media (max-width: 768px) {
  .speaker-details-2 .speaker-profile-card .profile-image-wrapper img {
    height: 280px;
  }
}

.speaker-details-2
  .speaker-profile-card
  .profile-image-wrapper
  .speaker-status {
  position: absolute;
  top: 1rem;
  right: 1rem;
}

.speaker-details-2
  .speaker-profile-card
  .profile-image-wrapper
  .speaker-status
  .status-badge {
  background: var(--accent-color);
  color: var(--contrast-color);
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.speaker-details-2
  .speaker-profile-card
  .profile-image-wrapper
  .speaker-status
  .status-badge.keynote {
  background: linear-gradient(
    135deg,
    var(--accent-color),
    color-mix(in srgb, var(--accent-color), #ff6b6b 30%)
  );
}

.speaker-details-2 .speaker-profile-card .profile-content {
  padding: 2rem;
}

.speaker-details-2 .speaker-profile-card .profile-content .speaker-name {
  font-size: 1.8rem;
  margin-bottom: 0.5rem;
  color: var(--heading-color);
}

.speaker-details-2 .speaker-profile-card .profile-content .speaker-title {
  color: var(--accent-color);
  font-size: 1.1rem;
  font-weight: 500;
  margin-bottom: 0.75rem;
}

.speaker-details-2 .speaker-profile-card .profile-content .speaker-company {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: color-mix(in srgb, var(--default-color), transparent 30%);
  margin-bottom: 2rem;
  font-size: 1rem;
}

.speaker-details-2 .speaker-profile-card .profile-content .speaker-company i {
  color: var(--accent-color);
}

.speaker-details-2 .speaker-profile-card .profile-content .connection-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: color-mix(in srgb, var(--accent-color), transparent 95%);
  border-radius: 12px;
}

.speaker-details-2
  .speaker-profile-card
  .profile-content
  .connection-stats
  .stat-item {
  text-align: center;
}

.speaker-details-2
  .speaker-profile-card
  .profile-content
  .connection-stats
  .stat-item
  .stat-number {
  display: block;
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--accent-color);
  line-height: 1.2;
}

.speaker-details-2
  .speaker-profile-card
  .profile-content
  .connection-stats
  .stat-item
  .stat-label {
  font-size: 0.8rem;
  color: color-mix(in srgb, var(--default-color), transparent 40%);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.speaker-details-2 .speaker-profile-card .profile-content .social-connections {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.speaker-details-2
  .speaker-profile-card
  .profile-content
  .social-connections
  .social-btn {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  background: transparent;
  border: 2px solid color-mix(in srgb, var(--default-color), transparent 85%);
  border-radius: 10px;
  color: var(--default-color);
  text-decoration: none;
  transition: all 0.3s ease;
  font-weight: 500;
}

.speaker-details-2
  .speaker-profile-card
  .profile-content
  .social-connections
  .social-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px
    color-mix(in srgb, var(--default-color), transparent 85%);
}

.speaker-details-2
  .speaker-profile-card
  .profile-content
  .social-connections
  .social-btn.linkedin:hover {
  border-color: #0077b5;
  color: #0077b5;
  background: color-mix(in srgb, #0077b5, transparent 95%);
}

.speaker-details-2
  .speaker-profile-card
  .profile-content
  .social-connections
  .social-btn.twitter:hover {
  border-color: #1da1f2;
  color: #1da1f2;
  background: color-mix(in srgb, #1da1f2, transparent 95%);
}

.speaker-details-2
  .speaker-profile-card
  .profile-content
  .social-connections
  .social-btn.website:hover {
  border-color: var(--accent-color);
  color: var(--accent-color);
  background: color-mix(in srgb, var(--accent-color), transparent 95%);
}

.speaker-details-2 .speaker-highlight {
  background: linear-gradient(
    135deg,
    var(--surface-color),
    color-mix(in srgb, var(--accent-color), transparent 97%)
  );
  border-radius: 15px;
  padding: 2.5rem;
  margin-bottom: 2.5rem;
  position: relative;
  border: 1px solid color-mix(in srgb, var(--accent-color), transparent 85%);
}

.speaker-details-2 .speaker-highlight .quote-container {
  position: relative;
}

.speaker-details-2 .speaker-highlight .quote-container .quote-icon {
  position: absolute;
  top: -15px;
  left: -10px;
  width: 50px;
  height: 50px;
  background: var(--accent-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.speaker-details-2 .speaker-highlight .quote-container .quote-icon i {
  font-size: 1.5rem;
  color: var(--contrast-color);
}

.speaker-details-2 .speaker-highlight .quote-container blockquote {
  font-size: 1.25rem;
  font-style: italic;
  line-height: 1.6;
  color: var(--heading-color);
  margin: 0;
  padding-left: 3rem;
}

@media (max-width: 768px) {
  .speaker-details-2 .speaker-highlight .quote-container blockquote {
    font-size: 1.1rem;
    padding-left: 2rem;
  }
}

.speaker-details-2 .speaker-biography,
.speaker-details-2 .session-schedule {
  margin-bottom: 2.5rem;
}

.speaker-details-2 .speaker-biography .section-header,
.speaker-details-2 .session-schedule .section-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 2rem;
}

.speaker-details-2 .speaker-biography .section-header h3,
.speaker-details-2 .session-schedule .section-header h3 {
  font-size: 1.6rem;
  color: var(--heading-color);
  margin: 0;
  white-space: nowrap;
}

.speaker-details-2 .speaker-biography .section-header .header-line,
.speaker-details-2 .session-schedule .section-header .header-line {
  flex: 1;
  height: 2px;
  background: linear-gradient(90deg, var(--accent-color), transparent);
}

.speaker-details-2 .speaker-biography .bio-content p,
.speaker-details-2 .session-schedule .bio-content p {
  font-size: 1.05rem;
  line-height: 1.7;
  margin-bottom: 1.5rem;
  color: color-mix(in srgb, var(--default-color), transparent 10%);
}

.speaker-details-2 .speaker-biography .bio-content .expertise-areas,
.speaker-details-2 .session-schedule .bio-content .expertise-areas {
  margin-top: 2rem;
}

.speaker-details-2 .speaker-biography .bio-content .expertise-areas h4,
.speaker-details-2 .session-schedule .bio-content .expertise-areas h4 {
  font-size: 1.2rem;
  margin-bottom: 1rem;
  color: var(--heading-color);
}

.speaker-details-2 .speaker-biography .bio-content .expertise-areas .tags-list,
.speaker-details-2 .session-schedule .bio-content .expertise-areas .tags-list {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
}

.speaker-details-2
  .speaker-biography
  .bio-content
  .expertise-areas
  .tags-list
  .expertise-tag,
.speaker-details-2
  .session-schedule
  .bio-content
  .expertise-areas
  .tags-list
  .expertise-tag {
  background: var(--accent-color);
  color: var(--contrast-color);
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 500;
}

.speaker-details-2 .schedule-timeline {
  position: relative;
  padding-left: 2rem;
}

.speaker-details-2 .schedule-timeline::before {
  content: "";
  position: absolute;
  left: 15px;
  top: 0;
  bottom: 0;
  width: 2px;
  background: color-mix(in srgb, var(--accent-color), transparent 70%);
}

.speaker-details-2 .schedule-timeline .timeline-item {
  position: relative;
  margin-bottom: 2.5rem;
}

.speaker-details-2 .schedule-timeline .timeline-item:last-child {
  margin-bottom: 0;
}

.speaker-details-2
  .schedule-timeline
  .timeline-item.featured
  .timeline-content {
  background: color-mix(in srgb, var(--accent-color), transparent 95%);
  border-left: 4px solid var(--accent-color);
}

.speaker-details-2 .schedule-timeline .timeline-item .timeline-marker {
  position: absolute;
  left: -2rem;
  top: 0.5rem;
}

.speaker-details-2
  .schedule-timeline
  .timeline-item
  .timeline-marker
  .marker-dot {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  margin-left: 8px;
  border: 3px solid var(--surface-color);
}

.speaker-details-2
  .schedule-timeline
  .timeline-item
  .timeline-marker
  .marker-dot.keynote {
  background: var(--accent-color);
}

.speaker-details-2
  .schedule-timeline
  .timeline-item
  .timeline-marker
  .marker-dot.workshop {
  background: #10b981;
}

.speaker-details-2
  .schedule-timeline
  .timeline-item
  .timeline-marker
  .marker-dot.panel {
  background: #8b5cf6;
}

.speaker-details-2 .schedule-timeline .timeline-item .timeline-content {
  background: var(--surface-color);
  padding: 2rem;
  border-radius: 12px;
  border: 1px solid color-mix(in srgb, var(--default-color), transparent 90%);
}

.speaker-details-2
  .schedule-timeline
  .timeline-item
  .timeline-content
  .session-tag {
  display: inline-block;
  padding: 0.3rem 0.8rem;
  border-radius: 15px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  margin-bottom: 1rem;
}

.speaker-details-2
  .schedule-timeline
  .timeline-item
  .timeline-content
  .session-tag.keynote {
  background: var(--accent-color);
  color: var(--contrast-color);
}

.speaker-details-2
  .schedule-timeline
  .timeline-item
  .timeline-content
  .session-tag.workshop {
  background: #10b981;
  color: white;
}

.speaker-details-2
  .schedule-timeline
  .timeline-item
  .timeline-content
  .session-tag.panel {
  background: #8b5cf6;
  color: white;
}

.speaker-details-2
  .schedule-timeline
  .timeline-item
  .timeline-content
  .session-name {
  font-size: 1.3rem;
  margin-bottom: 1.5rem;
  color: var(--heading-color);
}

.speaker-details-2
  .schedule-timeline
  .timeline-item
  .timeline-content
  .session-meta-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 1rem;
}

@media (max-width: 768px) {
  .speaker-details-2
    .schedule-timeline
    .timeline-item
    .timeline-content
    .session-meta-grid {
    grid-template-columns: 1fr;
  }
}

.speaker-details-2
  .schedule-timeline
  .timeline-item
  .timeline-content
  .session-meta-grid
  .meta-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: color-mix(in srgb, var(--default-color), transparent 30%);
  font-size: 0.95rem;
}

.speaker-details-2
  .schedule-timeline
  .timeline-item
  .timeline-content
  .session-meta-grid
  .meta-item
  i {
  color: var(--accent-color);
  font-size: 1rem;
}

.speaker-details-2
  .schedule-timeline
  .timeline-item
  .timeline-content
  .session-summary {
  line-height: 1.6;
  margin: 0;
  color: color-mix(in srgb, var(--default-color), transparent 20%);
}

.speaker-details-2 .speaker-cta {
  padding: 2rem 0;
  border-top: 1px solid
    color-mix(in srgb, var(--default-color), transparent 90%);
}

.speaker-details-2 .speaker-cta .cta-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  justify-content: center;
}

@media (max-width: 768px) {
  .speaker-details-2 .speaker-cta .cta-buttons {
    flex-direction: column;
    align-items: stretch;
  }
}

.speaker-details-2 .speaker-cta .cta-buttons a {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.875rem 2rem;
  border-radius: 25px;
  font-weight: 600;
  text-decoration: none;
  transition: all 0.3s ease;
  justify-content: center;
}

.speaker-details-2 .speaker-cta .cta-buttons a.btn-primary {
  background: var(--accent-color);
  color: var(--contrast-color);
  border: 2px solid var(--accent-color);
}

.speaker-details-2 .speaker-cta .cta-buttons a.btn-primary:hover {
  background: transparent;
  color: var(--accent-color);
  transform: translateY(-2px);
}

.speaker-details-2 .speaker-cta .cta-buttons a.btn-secondary {
  background: var(--heading-color);
  color: var(--background-color);
  border: 2px solid var(--heading-color);
}

.speaker-details-2 .speaker-cta .cta-buttons a.btn-secondary:hover {
  background: transparent;
  color: var(--heading-color);
  transform: translateY(-2px);
}

.speaker-details-2 .speaker-cta .cta-buttons a.btn-outline {
  background: transparent;
  color: var(--default-color);
  border: 2px solid color-mix(in srgb, var(--default-color), transparent 70%);
}

.speaker-details-2 .speaker-cta .cta-buttons a.btn-outline:hover {
  background: var(--default-color);
  color: var(--background-color);
  border-color: var(--default-color);
  transform: translateY(-2px);
}

/*--------------------------------------------------------------
# Tickets Section
--------------------------------------------------------------*/
.tickets .ticket-card {
  background: var(--surface-color);
  border-radius: 16px;
  padding: 40px 30px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  position: relative;
  border: 2px solid transparent;
  height: 100%;
}

.tickets .ticket-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
}

.tickets .ticket-card.featured {
  border-color: var(--accent-color);
  transform: scale(1.05);
  background: linear-gradient(
    135deg,
    var(--surface-color) 0%,
    color-mix(in srgb, var(--accent-color), transparent 95%) 100%
  );
}

.tickets .ticket-card.featured:hover {
  transform: scale(1.05) translateY(-5px);
}

.tickets .popular-badge {
  position: absolute;
  top: -12px;
  left: 50%;
  transform: translateX(-50%);
  background: var(--accent-color);
  color: var(--contrast-color);
  padding: 8px 20px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.tickets .ticket-header {
  text-align: center;
  margin-bottom: 30px;
}

.tickets .ticket-header h3 {
  font-size: 28px;
  font-weight: 700;
  margin-bottom: 15px;
  color: var(--heading-color);
}

.tickets .ticket-header .ticket-price {
  margin-bottom: 10px;
}

.tickets .ticket-header .ticket-price .currency {
  font-size: 20px;
  color: var(--accent-color);
  font-weight: 600;
  vertical-align: top;
}

.tickets .ticket-header .ticket-price .amount {
  font-size: 48px;
  font-weight: 700;
  color: var(--accent-color);
  line-height: 1;
}

.tickets .ticket-header .ticket-price .period {
  font-size: 16px;
  color: color-mix(in srgb, var(--default-color), transparent 40%);
  font-weight: 500;
}

.tickets .ticket-header .ticket-price .original-price {
  font-size: 18px;
  color: color-mix(in srgb, var(--default-color), transparent 50%);
  text-decoration: line-through;
  margin-right: 10px;
}

.tickets .ticket-header .ticket-duration {
  color: color-mix(in srgb, var(--default-color), transparent 30%);
  font-size: 16px;
  font-weight: 500;
  margin: 0;
}

.tickets .ticket-body {
  margin-bottom: 30px;
}

.tickets .ticket-body .ticket-features {
  list-style: none;
  padding: 0;
  margin: 0;
}

.tickets .ticket-body .ticket-features li {
  display: flex;
  align-items: flex-start;
  margin-bottom: 15px;
  font-size: 15px;
  line-height: 1.5;
  color: var(--default-color);
}

.tickets .ticket-body .ticket-features li i {
  color: var(--accent-color);
  font-size: 18px;
  margin-right: 12px;
  margin-top: 2px;
  flex-shrink: 0;
}

.tickets .ticket-body .ticket-features li:last-child {
  margin-bottom: 0;
}

.tickets .ticket-footer {
  text-align: center;
}

.tickets .ticket-footer .btn-ticket {
  background: var(--accent-color);
  color: var(--contrast-color);
  padding: 15px 30px;
  border-radius: 50px;
  font-weight: 600;
  font-size: 16px;
  text-decoration: none;
  display: inline-block;
  transition: all 0.3s ease;
  border: 2px solid var(--accent-color);
  width: 100%;
  margin-bottom: 15px;
}

.tickets .ticket-footer .btn-ticket:hover {
  background: var(--contrast-color);
  color: var(--accent-color);
  transform: translateY(-2px);
}

.tickets .ticket-footer .availability-info {
  margin: 0;
  font-size: 14px;
  color: color-mix(in srgb, var(--default-color), transparent 40%);
  font-weight: 500;
}

.tickets .ticket-info-bar {
  background: color-mix(in srgb, var(--accent-color), transparent 95%);
  border-radius: 16px;
  padding: 40px;
  text-align: center;
  border: 1px solid color-mix(in srgb, var(--accent-color), transparent 85%);
}

.tickets .ticket-info-bar .countdown-info {
  margin-bottom: 30px;
}

.tickets .ticket-info-bar .countdown-info h4 {
  color: var(--heading-color);
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 20px;
}

.tickets .ticket-info-bar .countdown-info h4 i {
  color: var(--accent-color);
  margin-right: 10px;
}

.tickets .ticket-info-bar .countdown-info .countdown {
  display: flex;
  justify-content: center;
  gap: 30px;
}

.tickets .ticket-info-bar .countdown-info .countdown > div {
  background: var(--surface-color);
  padding: 20px 15px;
  border-radius: 10px;
  min-width: 80px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.tickets .ticket-info-bar .countdown-info .countdown > div h3 {
  font-size: 32px;
  font-weight: 700;
  color: var(--accent-color);
  margin: 0 0 5px 0;
  line-height: 1;
}

.tickets .ticket-info-bar .countdown-info .countdown > div h4 {
  font-size: 14px;
  font-weight: 500;
  color: color-mix(in srgb, var(--default-color), transparent 30%);
  margin: 0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.tickets .ticket-info-bar .support-info p {
  margin-bottom: 10px;
  font-size: 16px;
  color: var(--default-color);
}

.tickets .ticket-info-bar .support-info .contact-link {
  color: var(--accent-color);
  text-decoration: none;
  font-weight: 600;
}

.tickets .ticket-info-bar .support-info .contact-link:hover {
  color: color-mix(in srgb, var(--accent-color), transparent 25%);
}

.tickets .ticket-info-bar .support-info .divider {
  margin: 0 15px;
  color: color-mix(in srgb, var(--default-color), transparent 50%);
}

@media (max-width: 768px) {
  .tickets .ticket-card {
    padding: 30px 20px;
  }

  .tickets .ticket-card.featured {
    transform: none;
  }

  .tickets .ticket-card.featured:hover {
    transform: translateY(-5px);
  }

  .tickets .ticket-header .ticket-price .amount {
    font-size: 36px;
  }

  .tickets .ticket-info-bar {
    padding: 30px 20px;
  }

  .tickets .ticket-info-bar .countdown {
    gap: 15px !important;
  }

  .tickets .ticket-info-bar .countdown > div {
    padding: 15px 10px;
    min-width: 60px;
  }

  .tickets .ticket-info-bar .countdown > div h3 {
    font-size: 24px;
  }

  .tickets .ticket-info-bar .countdown > div h4 {
    font-size: 12px;
  }

  .tickets .ticket-info-bar .support-info .divider {
    display: block;
    margin: 10px 0;
  }
}

/*--------------------------------------------------------------
# Buy Tickets Section
--------------------------------------------------------------*/
.buy-tickets {
  background-color: color-mix(
    in srgb,
    var(--background-color),
    var(--surface-color) 5%
  );
}

.buy-tickets .ticket-form-wrapper {
  background-color: var(--surface-color);
  border-radius: 12px;
  padding: 40px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  border: 1px solid color-mix(in srgb, var(--default-color), transparent 90%);
}

.buy-tickets .event-info {
  text-align: center;
  border-bottom: 2px solid
    color-mix(in srgb, var(--accent-color), transparent 80%);
  padding-bottom: 30px;
}

.buy-tickets .event-info h3 {
  color: var(--heading-color);
  margin-bottom: 20px;
  font-size: 2rem;
  font-weight: 700;
}

.buy-tickets .event-info .event-meta {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  gap: 30px;
}

.buy-tickets .event-info .event-meta span {
  display: flex;
  align-items: center;
  color: var(--default-color);
  font-size: 14px;
}

.buy-tickets .event-info .event-meta span i {
  color: var(--accent-color);
  margin-right: 8px;
  font-size: 16px;
}

@media (max-width: 768px) {
  .buy-tickets .event-info .event-meta {
    flex-direction: column;
    gap: 15px;
  }
}

.buy-tickets .ticket-types {
  margin-bottom: 40px;
}

.buy-tickets .ticket-types h4 {
  color: var(--heading-color);
  margin-bottom: 25px;
  font-size: 1.5rem;
  font-weight: 600;
}

.buy-tickets .ticket-types .ticket-option {
  margin-bottom: 20px;
}

.buy-tickets .ticket-types .ticket-option input[type="radio"] {
  display: none;
}

.buy-tickets .ticket-types .ticket-option .ticket-label {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 25px;
  border: 2px solid color-mix(in srgb, var(--default-color), transparent 85%);
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  background-color: var(--surface-color);
}

.buy-tickets .ticket-types .ticket-option .ticket-label:hover {
  border-color: color-mix(in srgb, var(--accent-color), transparent 50%);
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.buy-tickets
  .ticket-types
  .ticket-option
  input[type="radio"]:checked
  + .ticket-label {
  border-color: var(--accent-color);
  background-color: color-mix(in srgb, var(--accent-color), transparent 95%);
  box-shadow: 0 5px 20px
    color-mix(in srgb, var(--accent-color), transparent 80%);
}

.buy-tickets .ticket-types .ticket-option .ticket-info {
  flex: 1;
}

.buy-tickets .ticket-types .ticket-option .ticket-info .ticket-name {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--heading-color);
  margin-bottom: 8px;
}

.buy-tickets .ticket-types .ticket-option .ticket-info .ticket-description {
  color: var(--default-color);
  font-size: 14px;
  margin-bottom: 12px;
}

.buy-tickets .ticket-types .ticket-option .ticket-info .ticket-benefits {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.buy-tickets .ticket-types .ticket-option .ticket-info .ticket-benefits span {
  font-size: 12px;
  color: color-mix(in srgb, var(--default-color), transparent 20%);
  background-color: color-mix(in srgb, var(--accent-color), transparent 90%);
  padding: 4px 8px;
  border-radius: 4px;
}

.buy-tickets .ticket-types .ticket-option .ticket-price {
  text-align: right;
}

.buy-tickets .ticket-types .ticket-option .ticket-price .original-price {
  color: color-mix(in srgb, var(--default-color), transparent 50%);
  text-decoration: line-through;
  font-size: 14px;
  display: block;
  margin-bottom: 4px;
}

.buy-tickets .ticket-types .ticket-option .ticket-price .current-price {
  color: var(--accent-color);
  font-size: 1.5rem;
  font-weight: 700;
}

@media (max-width: 768px) {
  .buy-tickets .ticket-types .ticket-option .ticket-label {
    flex-direction: column;
    text-align: center;
    gap: 15px;
  }

  .buy-tickets .ticket-types .ticket-option .ticket-benefits {
    justify-content: center;
  }
}

.buy-tickets .ticket-form .form-group {
  margin-bottom: 25px;
}

.buy-tickets .ticket-form .form-group label {
  color: var(--heading-color);
  font-weight: 500;
  margin-bottom: 8px;
  display: block;
  font-size: 14px;
}

.buy-tickets .ticket-form .form-group input[type="text"],
.buy-tickets .ticket-form .form-group input[type="email"],
.buy-tickets .ticket-form .form-group input[type="tel"],
.buy-tickets .ticket-form .form-group select,
.buy-tickets .ticket-form .form-group textarea {
  color: var(--default-color);
  background-color: var(--surface-color);
  font-size: 14px;
  border: 2px solid color-mix(in srgb, var(--default-color), transparent 85%);
  border-radius: 8px;
  padding: 12px 16px;
  width: 100%;
  transition: all 0.3s ease;
}

.buy-tickets .ticket-form .form-group input[type="text"]:focus,
.buy-tickets .ticket-form .form-group input[type="email"]:focus,
.buy-tickets .ticket-form .form-group input[type="tel"]:focus,
.buy-tickets .ticket-form .form-group select:focus,
.buy-tickets .ticket-form .form-group textarea:focus {
  border-color: var(--accent-color);
  outline: none;
  box-shadow: 0 0 0 3px color-mix(in srgb, var(--accent-color), transparent 90%);
}

.buy-tickets .ticket-form .form-group input[type="text"]::placeholder,
.buy-tickets .ticket-form .form-group input[type="email"]::placeholder,
.buy-tickets .ticket-form .form-group input[type="tel"]::placeholder,
.buy-tickets .ticket-form .form-group select::placeholder,
.buy-tickets .ticket-form .form-group textarea::placeholder {
  color: color-mix(in srgb, var(--default-color), transparent 70%);
}

.buy-tickets .ticket-form .form-group select {
  appearance: none;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 12px center;
  background-repeat: no-repeat;
  background-size: 16px;
  padding-right: 40px;
}

.buy-tickets .pricing-summary {
  background-color: color-mix(in srgb, var(--accent-color), transparent 95%);
  border: 1px solid color-mix(in srgb, var(--accent-color), transparent 80%);
  border-radius: 10px;
  padding: 25px;
  margin: 30px 0;
}

.buy-tickets .pricing-summary h5 {
  color: var(--heading-color);
  margin-bottom: 20px;
  font-size: 1.2rem;
  font-weight: 600;
}

.buy-tickets .pricing-summary .summary-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12px;
  color: var(--default-color);
}

.buy-tickets .pricing-summary .summary-row.total {
  margin-top: 15px;
  padding-top: 15px;
  border-top: 2px solid color-mix(in srgb, var(--accent-color), transparent 70%);
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--heading-color);
}

.buy-tickets .pricing-summary .summary-row.total .total-price {
  color: var(--accent-color);
  font-size: 1.3rem;
  font-weight: 700;
}

.buy-tickets .pricing-summary .tax-note {
  font-size: 12px;
  color: color-mix(in srgb, var(--default-color), transparent 40%);
  text-align: center;
  margin-top: 15px;
  font-style: italic;
}

.buy-tickets .terms-checkbox,
.buy-tickets .newsletter-checkbox {
  margin-bottom: 20px;
}

.buy-tickets .terms-checkbox input[type="checkbox"],
.buy-tickets .newsletter-checkbox input[type="checkbox"] {
  margin-right: 10px;
}

.buy-tickets .terms-checkbox label,
.buy-tickets .newsletter-checkbox label {
  color: var(--default-color);
  font-size: 14px;
  cursor: pointer;
}

.buy-tickets .terms-checkbox label a,
.buy-tickets .newsletter-checkbox label a {
  color: var(--accent-color);
  text-decoration: none;
}

.buy-tickets .terms-checkbox label a:hover,
.buy-tickets .newsletter-checkbox label a:hover {
  text-decoration: underline;
}

.buy-tickets .form-submit {
  text-align: center;
  margin-top: 30px;
}

.buy-tickets .form-submit .btn-submit {
  background: linear-gradient(
    135deg,
    var(--accent-color),
    color-mix(in srgb, var(--accent-color), #000 15%)
  );
  color: var(--contrast-color);
  border: none;
  padding: 16px 40px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 250px;
}

.buy-tickets .form-submit .btn-submit:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px
    color-mix(in srgb, var(--accent-color), transparent 70%);
}

.buy-tickets .form-submit .btn-submit:active {
  transform: translateY(0);
}

.buy-tickets .security-info {
  margin-top: 40px;
  text-align: center;
  border-top: 1px solid
    color-mix(in srgb, var(--default-color), transparent 90%);
  padding-top: 30px;
}

.buy-tickets .security-info .security-badges {
  display: flex;
  justify-content: center;
  gap: 30px;
  margin-bottom: 25px;
}

.buy-tickets .security-info .security-badges .badge-item {
  display: flex;
  align-items: center;
  gap: 8px;
  color: color-mix(in srgb, var(--default-color), transparent 20%);
  font-size: 14px;
}

.buy-tickets .security-info .security-badges .badge-item i {
  color: var(--accent-color);
  font-size: 18px;
}

.buy-tickets .security-info .payment-methods {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15px;
  flex-wrap: wrap;
}

.buy-tickets .security-info .payment-methods .payment-label {
  color: var(--default-color);
  font-size: 14px;
  font-weight: 500;
}

.buy-tickets .security-info .payment-methods .payment-icons {
  display: flex;
  align-items: center;
  gap: 10px;
}

.buy-tickets .security-info .payment-methods .payment-icons i {
  color: var(--accent-color);
  font-size: 24px;
}

.buy-tickets .security-info .payment-methods .payment-icons .payment-text {
  color: color-mix(in srgb, var(--default-color), transparent 30%);
  font-size: 13px;
}

@media (max-width: 768px) {
  .buy-tickets .security-info .security-badges {
    flex-direction: column;
    gap: 15px;
  }

  .buy-tickets .security-info .payment-methods {
    flex-direction: column;
    gap: 10px;
  }
}

@media (max-width: 768px) {
  .buy-tickets .ticket-form-wrapper {
    padding: 25px 20px;
  }
}

/*--------------------------------------------------------------
# Venue 2 Section
--------------------------------------------------------------*/
.venue-2 .venue-content h3 {
  color: var(--heading-color);
  margin-bottom: 20px;
  font-size: 2rem;
}

.venue-2 .venue-content .venue-address {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 20px;
  color: var(--accent-color);
  font-weight: 500;
}

.venue-2 .venue-content .venue-address i {
  font-size: 1.2rem;
}

.venue-2 .venue-content p {
  margin-bottom: 30px;
  line-height: 1.7;
}

.venue-2 .venue-image {
  position: relative;
}

.venue-2 .venue-image img {
  border-radius: 15px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.venue-2 .venue-image .venue-badge {
  position: absolute;
  top: 20px;
  left: 20px;
  background: var(--accent-color);
  color: var(--contrast-color);
  padding: 8px 16px;
  border-radius: 25px;
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 0.9rem;
  font-weight: 500;
}

.venue-2 .venue-features {
  margin-bottom: 30px;
}

.venue-2 .venue-features .feature-item {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 15px;
}

.venue-2 .venue-features .feature-item i {
  color: var(--accent-color);
  font-size: 1.1rem;
  width: 20px;
}

.venue-2 .venue-features .feature-item span {
  font-weight: 500;
}

.venue-2 .venue-actions {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
}

.venue-2 .venue-actions .btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  border-radius: 25px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.venue-2 .venue-actions .btn.btn-primary {
  background-color: var(--accent-color);
  border-color: var(--accent-color);
  color: var(--contrast-color);
}

.venue-2 .venue-actions .btn.btn-primary:hover {
  background-color: color-mix(in srgb, var(--accent-color), black 10%);
  border-color: color-mix(in srgb, var(--accent-color), black 10%);
  transform: translateY(-2px);
}

.venue-2 .venue-actions .btn.btn-outline-primary {
  border-color: var(--accent-color);
  color: var(--accent-color);
}

.venue-2 .venue-actions .btn.btn-outline-primary:hover {
  background-color: var(--accent-color);
  color: var(--contrast-color);
  transform: translateY(-2px);
}

.venue-2 .venue-map h4 {
  color: var(--heading-color);
  margin-bottom: 25px;
  text-align: center;
}

.venue-2 .venue-map .map-container {
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.venue-2 .venue-map .map-container iframe {
  width: 100%;
  height: 400px;
}

.venue-2 .travel-info {
  text-align: center;
  padding: 30px 20px;
  background-color: var(--surface-color);
  border-radius: 15px;
  height: 100%;
  transition: transform 0.3s ease;
}

.venue-2 .travel-info:hover {
  transform: translateY(-5px);
}

.venue-2 .travel-info .travel-icon {
  width: 70px;
  height: 70px;
  margin: 0 auto 20px;
  background-color: color-mix(in srgb, var(--accent-color), transparent 10%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.venue-2 .travel-info .travel-icon i {
  font-size: 1.8rem;
  color: var(--contrast-color);
}

.venue-2 .travel-info h5 {
  color: var(--heading-color);
  margin-bottom: 15px;
}

.venue-2 .travel-info p {
  margin: 0;
  line-height: 1.6;
}

.venue-2 .accommodation-section h4 {
  color: var(--heading-color);
  margin-bottom: 30px;
  text-align: center;
}

.venue-2 .accommodation-section .hotel-card {
  background-color: var(--surface-color);
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
  height: 100%;
}

.venue-2 .accommodation-section .hotel-card:hover {
  transform: translateY(-5px);
}

.venue-2 .accommodation-section .hotel-card img {
  width: 100%;
  height: 200px;
  object-fit: cover;
}

.venue-2 .accommodation-section .hotel-card .hotel-info {
  padding: 20px;
}

.venue-2 .accommodation-section .hotel-card .hotel-info h6 {
  color: var(--heading-color);
  margin-bottom: 10px;
  font-weight: 600;
}

.venue-2 .accommodation-section .hotel-card .hotel-info .hotel-distance {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-bottom: 8px;
  font-size: 0.9rem;
}

.venue-2 .accommodation-section .hotel-card .hotel-info .hotel-distance i {
  color: var(--accent-color);
  font-size: 0.8rem;
}

.venue-2 .accommodation-section .hotel-card .hotel-info .hotel-price {
  font-weight: 600;
  color: var(--accent-color);
}

@media (max-width: 768px) {
  .venue-2 .venue-actions {
    justify-content: center;
  }

  .venue-2 .venue-actions .btn {
    flex: 1;
    min-width: 150px;
  }

  .venue-2 .venue-map .map-container iframe {
    height: 300px;
  }

  .venue-2 .travel-info {
    margin-bottom: 30px;
  }

  .venue-2 .hotel-card {
    margin-bottom: 30px;
  }
}

/*--------------------------------------------------------------
# Sponsors Section
--------------------------------------------------------------*/
.sponsors .clients-wrap {
  border-top: 1px solid
    color-mix(in srgb, var(--default-color), transparent 85%);
  border-left: 1px solid
    color-mix(in srgb, var(--default-color), transparent 85%);
}

.sponsors .client-logo {
  background-color: var(--surface-color);
  display: flex;
  justify-content: center;
  align-items: center;
  border-right: 1px solid
    color-mix(in srgb, var(--default-color), transparent 85%);
  border-bottom: 1px solid
    color-mix(in srgb, var(--default-color), transparent 85%);
  overflow: hidden;
}

.sponsors .client-logo img {
  padding: 50px;
  max-width: 80%;
  transition: 0.3s;
}

@media (max-width: 640px) {
  .sponsors .client-logo img {
    padding: 30px;
    max-width: 50%;
  }
}

.sponsors .client-logo:hover img {
  transform: scale(1.1);
}

/*--------------------------------------------------------------
# Contact Section
--------------------------------------------------------------*/
.contact .contact-info-box {
  background-color: var(--surface-color);
  border-radius: 10px;
  box-shadow: 0 5px 25px rgba(0, 0, 0, 0.05);
  padding: 25px;
  height: 100%;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  display: flex;
  align-items: flex-start;
  gap: 15px;
}

.contact .contact-info-box:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
}

.contact .contact-info-box .icon-box {
  background-color: color-mix(in srgb, var(--accent-color), transparent 90%);
  color: var(--accent-color);
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.contact .contact-info-box .icon-box i {
  font-size: 24px;
}

.contact .contact-info-box .info-content h4 {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 10px;
}

.contact .contact-info-box .info-content p {
  margin-bottom: 5px;
  color: color-mix(in srgb, var(--default-color), transparent 20%);
  font-size: 15px;
  line-height: 1.5;
}

.contact .contact-info-box .info-content p:last-child {
  margin-bottom: 0;
}

.contact .map-section {
  position: relative;
  width: 100%;
  height: 500px;
  overflow: hidden;
}

.contact .map-section iframe {
  display: block;
  width: 100%;
  height: 100%;
  border: 0;
}

.contact .form-container-overlap {
  position: relative;
  margin-top: -150px;
  margin-bottom: 60px;
  z-index: 10;
}

.contact .contact-form-wrapper {
  background-color: var(--surface-color);
  border-radius: 12px;
  box-shadow: 0 5px 25px rgba(0, 0, 0, 0.05);
  padding: 40px;
}

.contact .contact-form-wrapper h2 {
  font-size: 28px;
  font-weight: 700;
  margin-bottom: 30px;
  position: relative;
}

.contact .contact-form-wrapper h2:after {
  content: "";
  position: absolute;
  left: 50%;
  bottom: -10px;
  transform: translateX(-50%);
  width: 50px;
  height: 3px;
  background-color: var(--accent-color);
}

.contact .contact-form-wrapper .form-group {
  margin-bottom: 20px;
}

.contact .contact-form-wrapper .form-group .input-with-icon {
  position: relative;
}

.contact .contact-form-wrapper .form-group .input-with-icon i {
  position: absolute;
  left: 15px;
  top: 50%;
  transform: translateY(-50%);
  color: color-mix(in srgb, var(--default-color), transparent 40%);
  font-size: 18px;
  z-index: 10;
}

.contact .contact-form-wrapper .form-group .input-with-icon i.message-icon {
  top: 28px;
}

.contact .contact-form-wrapper .form-group .input-with-icon textarea + i {
  top: 25px;
  transform: none;
}

.contact .contact-form-wrapper .form-group .input-with-icon .form-control {
  border-radius: 8px;
  padding: 12px 15px 12px 45px;
  height: 3.5rem;
  color: var(--default-color);
  background-color: var(--surface-color);
  font-size: 15px;
  border: 1px solid color-mix(in srgb, var(--default-color), transparent 80%);
}

.contact
  .contact-form-wrapper
  .form-group
  .input-with-icon
  .form-control:focus {
  border-color: var(--accent-color);
  box-shadow: 0 0 0 0.25rem
    color-mix(in srgb, var(--accent-color), transparent 90%);
}

.contact
  .contact-form-wrapper
  .form-group
  .input-with-icon
  .form-control::placeholder {
  color: color-mix(in srgb, var(--default-color), transparent 40%);
}

.contact
  .contact-form-wrapper
  .form-group
  .input-with-icon
  textarea.form-control {
  height: 180px;
  resize: none;
  padding-top: 15px;
}

.contact .contact-form-wrapper .btn-submit {
  background-color: var(--accent-color);
  border: none;
  color: var(--contrast-color);
  padding: 12px 30px;
  font-size: 16px;
  font-weight: 600;
  letter-spacing: 1px;
  border-radius: 8px;
  transition: all 0.3s ease;
  box-shadow: 0 5px 15px
    color-mix(in srgb, var(--accent-color), transparent 70%);
}

.contact .contact-form-wrapper .btn-submit:hover {
  background-color: color-mix(in srgb, var(--accent-color), transparent 15%);
  transform: translateY(-3px);
  box-shadow: 0 8px 20px
    color-mix(in srgb, var(--accent-color), transparent 60%);
}

.contact .contact-form-wrapper .btn-submit:active {
  transform: translateY(0);
  box-shadow: 0 3px 10px
    color-mix(in srgb, var(--accent-color), transparent 70%);
}

.contact .contact-form-wrapper .loading,
.contact .contact-form-wrapper .error-message,
.contact .contact-form-wrapper .sent-message {
  margin-top: 10px;
  margin-bottom: 20px;
}

@media (max-width: 992px) {
  .contact .form-container-overlap {
    margin-top: -120px;
  }

  .contact .contact-form-wrapper {
    padding: 30px;
  }
}

@media (max-width: 768px) {
  .contact .contact-info-box {
    margin-bottom: 20px;
  }

  .contact .form-container-overlap {
    margin-top: -100px;
  }

  .contact .contact-form-wrapper {
    padding: 25px;
  }

  .contact .contact-form-wrapper h2 {
    font-size: 24px;
  }

  .contact .map-section {
    height: 450px;
  }
}

@media (max-width: 576px) {
  .contact .form-container-overlap {
    margin-top: -80px;
  }

  .contact .contact-form-wrapper {
    padding: 20px;
  }

  .contact .btn-submit {
    width: 100%;
  }

  .contact .map-section {
    height: 400px;
  }
}

/*--------------------------------------------------------------
# Terms Of Service Section
--------------------------------------------------------------*/
.terms-of-service .tos-header {
  margin-bottom: 60px;
}

.terms-of-service .tos-header .last-updated {
  display: inline-block;
  padding: 8px 20px;
  background-color: color-mix(in srgb, var(--accent-color), transparent 90%);
  border-radius: 30px;
  color: var(--accent-color);
  font-size: 0.95rem;
  margin-bottom: 20px;
}

.terms-of-service .tos-header h2 {
  font-size: 2.5rem;
  margin-bottom: 15px;
}

.terms-of-service .tos-header p {
  color: color-mix(in srgb, var(--default-color), transparent 30%);
  font-size: 1.1rem;
  max-width: 700px;
  margin: 0 auto;
}

.terms-of-service .tos-content .content-section {
  margin-bottom: 50px;
  scroll-margin-top: 100px;
}

.terms-of-service .tos-content .content-section:last-child {
  margin-bottom: 0;
}

.terms-of-service .tos-content .content-section h3 {
  font-size: 1.8rem;
  margin-bottom: 20px;
  color: var(--heading-color);
}

.terms-of-service .tos-content .content-section p {
  color: color-mix(in srgb, var(--default-color), transparent 20%);
  line-height: 1.7;
  margin-bottom: 20px;
}

.terms-of-service .tos-content .content-section p:last-child {
  margin-bottom: 0;
}

.terms-of-service .tos-content .content-section .info-box {
  display: flex;
  align-items: flex-start;
  gap: 15px;
  padding: 20px;
  background-color: color-mix(in srgb, var(--accent-color), transparent 95%);
  border-radius: 15px;
  margin-top: 20px;
}

.terms-of-service .tos-content .content-section .info-box i {
  font-size: 1.5rem;
  color: var(--accent-color);
  flex-shrink: 0;
}

.terms-of-service .tos-content .content-section .info-box p {
  margin: 0;
  font-size: 0.95rem;
}

.terms-of-service .tos-content .content-section .list-items {
  list-style: none;
  padding: 0;
  margin: 20px 0;
}

.terms-of-service .tos-content .content-section .list-items li {
  position: relative;
  padding-left: 25px;
  margin-bottom: 12px;
  color: color-mix(in srgb, var(--default-color), transparent 20%);
}

.terms-of-service .tos-content .content-section .list-items li:last-child {
  margin-bottom: 0;
}

.terms-of-service .tos-content .content-section .list-items li::before {
  content: "";
  position: absolute;
  left: 0;
  top: 10px;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: var(--accent-color);
}

.terms-of-service .tos-content .content-section .alert-box {
  display: flex;
  gap: 20px;
  padding: 25px;
  background-color: var(--surface-color);
  border-radius: 15px;
  border-left: 4px solid var(--accent-color);
  margin-top: 20px;
}

.terms-of-service .tos-content .content-section .alert-box i {
  font-size: 2rem;
  color: var(--accent-color);
  flex-shrink: 0;
}

.terms-of-service .tos-content .content-section .alert-box .alert-content h5 {
  font-size: 1.1rem;
  margin-bottom: 8px;
}

.terms-of-service .tos-content .content-section .alert-box .alert-content p {
  margin: 0;
  font-size: 0.95rem;
}

.terms-of-service .tos-content .content-section .prohibited-list {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
  margin-top: 20px;
}

@media (max-width: 576px) {
  .terms-of-service .tos-content .content-section .prohibited-list {
    grid-template-columns: 1fr;
  }
}

.terms-of-service
  .tos-content
  .content-section
  .prohibited-list
  .prohibited-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 15px;
  background-color: var(--surface-color);
  border-radius: 12px;
}

.terms-of-service
  .tos-content
  .content-section
  .prohibited-list
  .prohibited-item
  i {
  color: #dc3545;
  font-size: 1.2rem;
}

.terms-of-service
  .tos-content
  .content-section
  .prohibited-list
  .prohibited-item
  span {
  font-size: 0.95rem;
  color: color-mix(in srgb, var(--default-color), transparent 20%);
}

.terms-of-service .tos-content .content-section .disclaimer-box {
  background-color: var(--surface-color);
  padding: 25px;
  border-radius: 15px;
  margin-top: 20px;
}

.terms-of-service .tos-content .content-section .disclaimer-box p {
  margin-bottom: 15px;
  font-weight: 500;
}

.terms-of-service .tos-content .content-section .disclaimer-box ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.terms-of-service .tos-content .content-section .disclaimer-box ul li {
  position: relative;
  padding-left: 25px;
  margin-bottom: 12px;
  color: color-mix(in srgb, var(--default-color), transparent 20%);
  font-size: 0.95rem;
}

.terms-of-service
  .tos-content
  .content-section
  .disclaimer-box
  ul
  li:last-child {
  margin-bottom: 0;
}

.terms-of-service .tos-content .content-section .disclaimer-box ul li::before {
  content: "•";
  position: absolute;
  left: 8px;
  color: var(--accent-color);
}

.terms-of-service .tos-content .content-section .notice-box {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 20px;
  background-color: color-mix(in srgb, var(--accent-color), transparent 95%);
  border-radius: 15px;
  margin-top: 20px;
}

.terms-of-service .tos-content .content-section .notice-box i {
  font-size: 1.5rem;
  color: var(--accent-color);
  flex-shrink: 0;
}

.terms-of-service .tos-content .content-section .notice-box p {
  margin: 0;
  font-size: 0.95rem;
}

.terms-of-service .tos-contact {
  margin-top: 60px;
}

.terms-of-service .tos-contact .contact-box {
  background: linear-gradient(
    135deg,
    color-mix(in srgb, var(--accent-color), transparent 95%) 0%,
    color-mix(in srgb, var(--accent-color), transparent 98%) 100%
  );
  border-radius: 20px;
  padding: 40px;
  display: flex;
  align-items: center;
  gap: 30px;
}

@media (max-width: 576px) {
  .terms-of-service .tos-contact .contact-box {
    flex-direction: column;
    text-align: center;
  }
}

.terms-of-service .tos-contact .contact-box .contact-icon {
  width: 60px;
  height: 60px;
  background-color: var(--accent-color);
  border-radius: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.terms-of-service .tos-contact .contact-box .contact-icon i {
  font-size: 1.8rem;
  color: var(--contrast-color);
}

.terms-of-service .tos-contact .contact-box .contact-content {
  flex: 1;
}

.terms-of-service .tos-contact .contact-box .contact-content h4 {
  font-size: 1.4rem;
  margin-bottom: 8px;
}

.terms-of-service .tos-contact .contact-box .contact-content p {
  color: color-mix(in srgb, var(--default-color), transparent 30%);
  margin-bottom: 15px;
}

.terms-of-service .tos-contact .contact-box .contact-content .contact-link {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 12px 25px;
  background-color: var(--accent-color);
  color: var(--contrast-color);
  border-radius: 30px;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.3s;
}

.terms-of-service
  .tos-contact
  .contact-box
  .contact-content
  .contact-link:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

@media print {
  .terms-of-service .tos-contact {
    display: none;
  }

  .terms-of-service .content-section {
    page-break-inside: avoid;
  }
}

/*--------------------------------------------------------------
# Privacy Section
--------------------------------------------------------------*/
.privacy {
  font-size: 1rem;
  line-height: 1.7;
}

.privacy .privacy-header {
  margin-bottom: 60px;
  text-align: center;
  border-bottom: 1px solid
    color-mix(in srgb, var(--default-color), transparent 90%);
  padding-bottom: 40px;
}

.privacy .privacy-header .header-content {
  max-width: 800px;
  margin: 0 auto;
}

.privacy .privacy-header .header-content .last-updated {
  font-size: 0.95rem;
  color: color-mix(in srgb, var(--default-color), transparent 40%);
  margin-bottom: 20px;
}

.privacy .privacy-header .header-content h1 {
  font-size: 2.8rem;
  color: var(--heading-color);
  margin-bottom: 20px;
  font-weight: 600;
}

.privacy .privacy-header .header-content .intro-text {
  font-size: 1.2rem;
  color: color-mix(in srgb, var(--default-color), transparent 20%);
  line-height: 1.6;
}

.privacy .privacy-content {
  max-width: 800px;
  margin: 0 auto 60px;
}

.privacy .privacy-content .content-section {
  margin-bottom: 50px;
}

.privacy .privacy-content .content-section:last-child {
  margin-bottom: 0;
}

.privacy .privacy-content .content-section h2 {
  font-size: 1.8rem;
  color: var(--heading-color);
  margin-bottom: 25px;
  font-weight: 600;
}

.privacy .privacy-content .content-section h3 {
  font-size: 1.4rem;
  color: var(--heading-color);
  margin: 30px 0 20px;
  font-weight: 500;
}

.privacy .privacy-content .content-section p {
  margin-bottom: 20px;
}

.privacy .privacy-content .content-section p:last-child {
  margin-bottom: 0;
}

.privacy .privacy-content .content-section ul {
  list-style: none;
  padding: 0;
  margin: 0 0 20px;
}

.privacy .privacy-content .content-section ul li {
  position: relative;
  padding-left: 25px;
  margin-bottom: 12px;
}

.privacy .privacy-content .content-section ul li:last-child {
  margin-bottom: 0;
}

.privacy .privacy-content .content-section ul li::before {
  content: "•";
  position: absolute;
  left: 8px;
  color: var(--accent-color);
}

.privacy .privacy-contact {
  max-width: 800px;
  margin: 0 auto;
  padding-top: 40px;
  border-top: 1px solid
    color-mix(in srgb, var(--default-color), transparent 90%);
}

.privacy .privacy-contact h2 {
  font-size: 1.8rem;
  color: var(--heading-color);
  margin-bottom: 20px;
  font-weight: 600;
}

.privacy .privacy-contact p {
  margin-bottom: 20px;
}

.privacy .privacy-contact .contact-details {
  background-color: var(--surface-color);
  padding: 25px;
  border-radius: 10px;
}

.privacy .privacy-contact .contact-details p {
  margin-bottom: 10px;
}

.privacy .privacy-contact .contact-details p:last-child {
  margin-bottom: 0;
}

.privacy .privacy-contact .contact-details p strong {
  color: var(--heading-color);
  font-weight: 600;
}

@media print {
  .privacy {
    font-size: 12pt;
    line-height: 1.5;
  }

  .privacy .privacy-header {
    text-align: left;
    border-bottom: 1pt solid #000;
    padding-bottom: 20pt;
    margin-bottom: 30pt;
  }

  .privacy h1 {
    font-size: 24pt;
  }

  .privacy h2 {
    font-size: 18pt;
    page-break-after: avoid;
  }

  .privacy h3 {
    font-size: 14pt;
    page-break-after: avoid;
  }

  .privacy p,
  .privacy ul {
    page-break-inside: avoid;
  }

  .privacy .contact-details {
    border: 1pt solid #000;
    padding: 15pt;
  }
}

@media (max-width: 767px) {
  .privacy .privacy-header {
    margin-bottom: 40px;
    padding-bottom: 30px;
  }

  .privacy .privacy-header .header-content h1 {
    font-size: 2.2rem;
  }

  .privacy .privacy-header .header-content .intro-text {
    font-size: 1.1rem;
  }

  .privacy .privacy-content .content-section {
    margin-bottom: 40px;
  }

  .privacy .privacy-content .content-section h2 {
    font-size: 1.6rem;
  }

  .privacy .privacy-content .content-section h3 {
    font-size: 1.3rem;
  }
}

/*--------------------------------------------------------------
# Error 404 Section
--------------------------------------------------------------*/
.error-404 {
  padding: 80px 0;
  margin: 0 auto;
}

.error-404 .error-icon {
  font-size: 5rem;
  color: color-mix(in srgb, var(--accent-color), transparent 15%);
}

.error-404 .error-code {
  font-size: clamp(6rem, 15vw, 12rem);
  font-weight: 800;
  color: color-mix(in srgb, var(--heading-color), transparent 10%);
  font-family: var(--heading-font);
  line-height: 1;
}

.error-404 .error-title {
  font-size: 2rem;
  color: var(--heading-color);
  font-weight: 600;
}

.error-404 .error-text {
  font-size: 1.1rem;
  color: color-mix(in srgb, var(--default-color), transparent 20%);
  max-width: 600px;
  margin: 0 auto;
}

.error-404 .search-box {
  max-width: 500px;
  margin: 0 auto;
}

.error-404 .search-box .input-group {
  border-radius: 50px;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.error-404 .search-box .form-control {
  border: 1px solid color-mix(in srgb, var(--default-color), transparent 90%);
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  color: var(--default-color);
  background-color: var(--surface-color);
  border-radius: 50px;
}

.error-404 .search-box .form-control:focus {
  box-shadow: none;
  border-color: var(--accent-color);
}

.error-404 .search-box .form-control::placeholder {
  color: color-mix(in srgb, var(--default-color), transparent 60%);
}

.error-404 .search-box .search-btn {
  background-color: var(--accent-color);
  color: var(--contrast-color);
  border: none;
  padding: 0.75rem 1.5rem;
  transition: all 0.3s ease;
}

.error-404 .search-box .search-btn:hover {
  background-color: color-mix(in srgb, var(--accent-color), transparent 15%);
}

.error-404 .error-action .btn-primary {
  padding: 0.75rem 2rem;
  font-size: 1.1rem;
  background-color: var(--accent-color);
  border: none;
  color: var(--contrast-color);
  border-radius: 50px;
  transition: all 0.3s ease;
}

.error-404 .error-action .btn-primary:hover {
  background-color: color-mix(in srgb, var(--accent-color), transparent 15%);
  transform: translateY(-2px);
}

@media (max-width: 768px) {
  .error-404 {
    padding: 60px 0;
  }

  .error-404 .error-code {
    font-size: clamp(4rem, 12vw, 8rem);
  }

  .error-404 .error-title {
    font-size: 1.5rem;
  }

  .error-404 .error-text {
    font-size: 1rem;
    padding: 0 20px;
  }

  .error-404 .search-box {
    margin: 0 20px;
  }
}

/*--------------------------------------------------------------
# Starter Section Section
--------------------------------------------------------------*/
.starter-section {
  /* Add your styles here */
}
.copyright-img {
  width: 7%;
}
.card {
  position: relative;
  z-index: 1;
  background-color: #fff;
  border: 1px solid #f87500;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
  box-sizing: border-box;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s, box-shadow 0.3s, z-index 0.3s;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  align-items: center;
  color: white;
  background-size: cover;
  background-position: center;
  overflow: hidden;
  text-decoration: none; /* Remove underline from links */
  color: inherit;
}
.card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(53, 51, 51, 0.853);
  border-radius: 8px;
  z-index: 0;
  transition: background-color 0.3s ease;
}
.card:hover::before {
  background: transparent;
}
.card:hover .card-icon {
  opacity: 0;
  transition: opacity 0.3s ease;
}
.card * {
  position: relative;
  z-index: 1;
}
.card:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 15px rgba(248, 117, 0, 0.7);
  z-index: 10;
}
.card-icon {
  font-size: 60px;
  color: #f87500;
  margin-bottom: 15px;
}
.card span {
  color: #f87500;
  font-size: 16px;
  font-weight: bold;
  display: block;
}
@media (max-width: 768px) {
  div[style*="flex: 1 1 calc(33.333% - 13.33px)"],
  div[style*="flex: 1 1 calc(25% - 15px)"] {
    flex: 1 1 100% !important;
  }
}
.logo-image {
  max-width: 140px;
  border-radius: 50%;
  filter: drop-shadow(0 0 10px #f87500) brightness(1.1);
  transition: transform 0.3s ease, filter 0.3s ease;
}
.logo-image:hover {
  /* transform: rotate(15deg) scale(1.1); */
  filter: drop-shadow(0 0 15px crimson) brightness(1.2);
}

.gallery-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  gap: 20px;
  margin-bottom: 40px;
}

.gallery-item {
  border-radius: 12px;
  overflow: hidden;
  transition: transform 0.2s, box-shadow 0.2s;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.07);
}

.gallery-item img {
  width: 100%;
  height: 210px;
  object-fit: cover;
  display: block;
}

.gallery-item:hover {
  transform: scale(1.04);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
}

.gallery-filters {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
  justify-content: center;
}

.filter-btn {
  background: #fff;
  color: #d97a0b;
  border-radius: 40px;
  border: 1.5px solid #d97a0b;
  padding: 10px 24px;
  cursor: pointer;
  font-weight: 700;
  transition: background 0.18s, color 0.18s;
}

.filter-btn.active,
.filter-btn:hover {
  background: #d97a0b;
  color: #fff;
}

@media (max-width: 1024px) {
  .gallery-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 640px) {
  .gallery-grid {
    grid-template-columns: 1fr;
  }
  .gallery-item img {
    height: 150px;
  }
}
